# Testing Guide

This document provides a guide to writing tests for this project. It covers the testing strategy, the tools used, and the best practices to follow.

## Testing Strategy

The testing strategy for this project is based on the following principles:

*   **Test behavior, not implementation**: Focus on what users see and do, not on the internal implementation of the components.
*   **Use descriptive test names**: Clearly state what is being tested.
*   **Arrange, Act, Assert**: Structure tests clearly.
*   **Mock external dependencies**: Keep tests isolated and fast.
*   **Test edge cases**: Handle error conditions and boundary cases.
*   **Use waitFor for async operations**: Handle state changes properly.
*   **Clean up after tests**: Clear mocks and reset state.

## Tools

The following tools are used for testing in this project:

*   **Vitest**: A testing framework for Vite projects.
*   **React Testing Library**: A library for testing React components.
*   **user-event**: A library for simulating user events.
*   **msw**: A library for mocking API requests.

## Best Practices

*   **Write tests for all new features and bug fixes.**
*   **Keep tests small and focused.**
*   **Use descriptive test names.**
*   **Use the `data-testid` attribute to select elements in tests.**
*   **Use the `waitFor` function to wait for asynchronous operations to complete.**
*   **Use the `vi.mock` function to mock external dependencies.**
*   **Use the `vi.fn` function to create mock functions.**
*   **Use the `vi.spyOn` function to spy on functions.**
*   **Use the `vi.clearAllMocks` function to clear all mocks before each test.**
*   **Use the `vi.resetAllMocks` function to reset all mocks after each test.**

## Running Tests

To run the tests, use the following command:

```bash
npm test
```

To run the tests in watch mode, use the following command:

```bash
npm run test:ui
```

To run the tests with coverage, use the following command:

```bash
npm run test:coverage
```
