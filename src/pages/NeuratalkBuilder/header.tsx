import React, { useState, useEffect, useRef } from 'react';
import { Pencil, ImageUp, X, FileImage } from 'lucide-react';
import Group_24426 from '../../../public/Group_24426.svg';
import { updateTitle, updateDomain, updateDescription } from '@/store/slices/chatbotSlice';
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { FloatingField } from '@/components/ui/floating-label';
import { useTranslation } from 'react-i18next';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';

// Supported image types
export const SUPPORTED_IMAGE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/jpg',
  'image/webp',
  'image/gif',
  'image/svg+xml',
];

const DOMAIN_OPTIONS_CONFIG = [
  { value: 'ecomm', labelKey: 'domains.ecomm' },
  { value: 'telecom', labelKey: 'domains.telecom' },
  { value: 'retail', labelKey: 'domains.retail' },
  { value: 'travel', labelKey: 'domains.travel' },
  { value: 'other', labelKey: 'domains.other' },
];

export default function Header() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { title, domain, description } = useAppSelector(state => state.chatbot);

  // Translate options at runtime
  const DOMAIN_OPTIONS = DOMAIN_OPTIONS_CONFIG.map(opt => ({
    value: opt.value,
    label: t(opt.labelKey),
  }));

  const [editableTitle, setEditableTitle] = useState(title || '');
  const [editableDomain, setEditableDomain] = useState('');
  const [editableDescription, setEditableDescription] = useState(description || '');
  const [dialogOpen, setDialogOpen] = useState(false);
  const isTitleFilled = editableTitle.trim() !== '';
  const isDomainSelected = editableDomain !== '';

  useEffect(() => {
    if (dialogOpen) {
      setEditableTitle(title || '');
      setEditableDomain('');
      setEditableDescription(description || '');
    }
  }, [dialogOpen, title, description]);

  const handleTitleSave = () => {
    if (!canSave) return;
    dispatch(updateTitle(editableTitle));
    dispatch(updateDomain(editableDomain));
    dispatch(updateDescription(editableDescription));
    setDialogOpen(false);
  };

  const handleTitleCancel = () => {
    setEditableTitle(title || '');
    setEditableDomain('');
    setEditableDescription(description || '');
    setDialogOpen(false);
  };

  const fileInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleImageChange = file => {
    if (!file) return;
    if (file.size <= 5 * 1024 * 1024) {
      if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
        alert(t('editor.unsupportedFile'));
        return;
      }
      setSelectedImage(file);
      setIsUploading(true);
      setUploadProgress(0);

      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsUploading(false);
            setPreviewUrl(URL.createObjectURL(file));
            return 100;
          }
          return prev + 10;
        });
      }, 100);
    } else {
      alert(t('editor.fileTooLarge'));
    }
  };

  const onFileInputChange = e => {
    const file = e.target.files?.[0];
    handleImageChange(file);
  };

  const handleDragOver = e => handleDragOverAndDragEnter(e);
  const handleDragEnter = e => handleDragOverAndDragEnter(e);
  const handleDragOverAndDragEnter = e => {
    e.preventDefault();
    e.stopPropagation();
    if (!isUploading) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = e => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = e => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    if (isUploading) return;

    const file = e.dataTransfer.files?.[0];
    if (file && SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      handleImageChange(file);
    } else {
      alert(t('editor.invalidImageFile'));
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setUploadProgress(0);
    setIsUploading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const isAnyFieldChanged =
    editableTitle.trim() !== (title || '').trim() ||
    editableDomain.trim() !== (domain || '').trim() ||
    editableDescription.trim() !== (description || '').trim();

  const isAllFieldsFilled =
    editableTitle.trim() !== '' &&
    editableDomain.trim() !== '' &&
    editableDescription.trim() !== '';

  const canSave = isAnyFieldChanged && isAllFieldsFilled;

  return (
    <div className="bg-white">
      <div className="px-6 py-3 flex justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="#">{t('navigation.neuraTalk')}</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t('navigation.create')}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        {/* <LanguageSwitcher /> */}
      </div>

      <div className="px-6 py-4 border-secondary-200">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="w-9 h-9 p-1 bg-secondary-200 rounded-full flex items-center justify-center">
              <img
                src={Group_24426}
                alt="Chatbot Icon"
                className="w-full h-full object-contain"
                data-testid="default-icon"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-sm font-medium text-black" data-testid="title">
                  {title || t('chatbot.untitled')}
                </h3>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger asChild>
                    <button type="button" aria-label="Edit Chatbot" data-testid="edit-button">
                      <Pencil className="w-3.5 h-3.5" data-testid="pencil-icon" />
                    </button>
                  </DialogTrigger>
                  <DialogContent className="fixed left-1/2 top-1/2 z-50 w-full max-w-md -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white shadow-lg focus:outline-none px-6">
                    <DialogHeader>
                      <DialogTitle className="font-medium uppercase">{t('common.edit')}</DialogTitle>
                    </DialogHeader>
                    <hr className="border-secondary-200 my-1 -mx-6 w-[calc(100%+3rem)]" />
                    <form
                      onSubmit={e => {
                        e.preventDefault();
                        handleTitleSave();
                      }}
                      className="space-y-6"
                    >
                      <FloatingField
                        label={t('editor.chatbotName')}
                        value={editableTitle}
                        onChange={e => setEditableTitle(e.target.value)}
                        type="text"
                        autoFocus
                        data-testid="title-input"
                      />
                      <FloatingField
                        label={t('editor.domain')}
                        as="select"
                        value={editableDomain}
                        onChange={value => setEditableDomain(value)}
                        options={DOMAIN_OPTIONS}
                        disabled={!isTitleFilled}
                        data-testid="domain-select"
                        id="domain-select"
                      />
                      <FloatingField
                        label={t('editor.description')}
                        as="textarea"
                        value={editableDescription}
                        onChange={e => setEditableDescription(e.target.value)}
                        disabled={!isTitleFilled || !isDomainSelected}
                        data-testid="description-textarea"
                      />
                      {/* Image upload section */}
                      <div className="relative bg-tertiary-50 w-full h-auto rounded-md overflow-hidden">
                        <input
                          ref={fileInputRef}
                          id="image-upload"
                          type="file"
                          accept={SUPPORTED_IMAGE_TYPES.join(',')}
                          className="hidden"
                          onChange={onFileInputChange}
                          disabled={isUploading}
                          data-testid="image-upload-input"
                        />
                        {!selectedImage && (
                          <label
                            htmlFor="image-upload"
                            className={`cursor-pointer w-full h-full py-4 flex flex-col items-center justify-center border-dashed border rounded-md text-tertiary-400 transition ${
                              isDragging
                                ? 'bg-tertiary-50 border-tertiary-400'
                                : 'hover:bg-tertiary-100'
                            }`}
                            onDragOver={handleDragOver}
                            onDragEnter={handleDragEnter}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            data-testid="image-upload-label"
                          >
                            <ImageUp className="w-8 h-8 mb-1" data-testid="image-up-icon" />
                            <div>{t('editor.uploadImage')}</div>
                            <div className="text-xs mt-1 text-tertiary-400">
                              {t('editor.uploadFormat')}
                            </div>
                          </label>
                        )}
                        {selectedImage && (
                          <div
                            className="w-full h-full flex items-center gap-2 p-2 bg-tertiary-50 rounded-md border"
                            data-testid="image-preview"
                          >
                            {previewUrl && !isUploading ? (
                              <img
                                src={previewUrl}
                                alt="Uploaded Image Preview"
                                className="w-8 h-8 object-cover"
                                data-testid="preview-image"
                              />
                            ) : (
                              <FileImage className="w-6 h-6 text-tertiary-400" />
                            )}
                            <span className="flex-1 text-tertiary-700 text-sm truncate">
                              {selectedImage.name}
                            </span>
                            {isUploading && (
                              <div className="flex-1">
                                <Progress value={uploadProgress} data-testid="upload-progress" />
                              </div>
                            )}
                            <button
                              type="button"
                              onClick={removeImage}
                              className="ml-2 bg-transparent rounded-full p-1 hover:bg-tertiary-100"
                              aria-label="Remove Image"
                              data-testid="remove-image-button"
                            >
                              <X className="w-6 h-6 text-tertiary-600" />
                            </button>
                          </div>
                        )}
                      </div>

                      <DialogFooter>
                        <DialogClose asChild>
                          <button
                            type="button"
                            onClick={handleTitleCancel}
                            className="px-6 py-2 w-28 rounded border text-tertiary-700 bg-white hover:bg-tertiary-100"
                            data-testid="cancel-button"
                          >
                            {t('common.cancel')}
                          </button>
                        </DialogClose>
                        <button
                          type="submit"
                          className="px-6 py-2 w-28 rounded bg-primary-600 text-white cursor-pointer"
                          disabled={!canSave}
                          data-testid="save-button"
                        >
                          {t('common.save')}
                        </button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>
              <div className="text-xs font-medium text-tertiary-500 mt-0.5" data-testid="domain">
                {domain || t('chatbot.noDomain')}
              </div>
              <div className="text-xs text-tertiary-500 mt-1" data-testid="description">
                {description || t('chatbot.noDescription')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
