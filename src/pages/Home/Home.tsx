import * as React from 'react';
import { Search, SlidersHorizontal, LayoutGrid, List, Funnel } from 'lucide-react';
import ChatbotCard from './ChatbotCard';
import DropdownButton from '@/components/dropdownButton';
import group from '../../assets/icons/Group_10874.svg';
import MicroFrontendWrapper from '@/components/micro-frontend-wrapper';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { AppDispatch, store } from '@/store/store';
import {
  selectAllChatbots,
  deleteChatbot,
  cloneChatbot,
  addChatbot,
} from '@/store/slices/chatbotsSlice';
import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { NavigateFunction } from 'react-router-dom';
import { RoutesName } from '@/lib/constant';
import { useTranslation } from 'react-i18next';

interface HomeProps {
  navigate: NavigateFunction;
}

const Home = ({navigate}: HomeProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const allChatbots = useSelector(selectAllChatbots);

  const [searchTerm, setSearchTerm] = useState<string>('');

  const filteredChatbots = useMemo(() => {
    if (!searchTerm) {
      return allChatbots;
    }
    const lowercasedValue = searchTerm.toLowerCase();
    return allChatbots.filter(bot => bot.title.toLowerCase().includes(lowercasedValue));
  }, [searchTerm, allChatbots]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleDeleteChatbot = (idToDelete: string) => {
    dispatch(deleteChatbot(idToDelete));
    console.log(`Dispatched delete action for chatbot ID: ${idToDelete}`);
  };

  const handleCloneChatbot = (idToClone: string) => {
    dispatch(cloneChatbot(idToClone));
    console.log(`Dispatched clone action for chatbot ID: ${idToClone}`);
  };

  const handleCreateChatbot = () => {
    dispatch(addChatbot()); // Dispatch the addChatbot action
    navigate(`${RoutesName.NEURA_TALK_BUILDER}`);
    console.log('Dispatched action to create new chatbot.');
  };

  return (
    <MicroFrontendWrapper>
      <div className="min-h-screen">
        <section className="relative flex p-0 h-[200px] shadow-none overflow-hidden">
          <img
            src={group}
            alt="NeuraTalk AI Background"
            className="absolute -top-56 left-0 w-full object-fill h-auto z-0"
          />
          <div className="absolute bottom-0 left-0 w-full h-6 bg-gradient-to-b from-transparent to-white z-10" />

          {/* Text content over the image */}
          <div className="absolute inset-0 w-1/2 flex flex-col justify-center px-6 top-6">
            <p className="text-tertiary-600 text-base max-w-3xl">
              <span className="font-medium text-tertiary-900 text-base">{t('home.title')} </span>
              {t('home.description')}
            </p>
          </div>

        </section>

        {/* Filters and Actions */}
        <div className="flex flex-col px-6 mb-40 mx-auto max-w-7xl">
          <section className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
            <div className="flex flex-col sm:flex-row items-center gap-3 w-full md:w-auto flex-wrap">
              <div className="relative sm:w-auto md:w-52">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="search"
                  placeholder={t('common.search')}
                  className="pl-10 pr-3 py-2 h-10 border border-slate-300 rounded-md w-full bg-white shadow-sm text-sm placeholder-tertiary-400"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
              <DropdownButton />
              <Button
                variant="outline"
                className="bg-white shadow-sm border-tertiary-300 text-slate-500 hover:bg-tertiary-50 w-full sm:w-auto h-10 px-4 py-2"
              >
                <Funnel className="h-4 w-4 mr-2 text-tertiary--500" />
                {t('common.filter')}
              </Button>
            </div>

            <div className="flex items-center gap-2 w-full sm:w-auto justify-start md:justify-end">
              <Button
                variant="outline"
                size="icon"
                className="bg-error-50 text-error-600 border-error-200 hover:bg-error-100 shadow-sm h-10 w-10"
              >
                <LayoutGrid className="h-5 w-5" />
              </Button>
              <Button
                variant="default"
                size="icon"
                className="bg-transparent text-tertiary-500 hover:border-tertiary-300 hover:bg-tertiary-200 hover:shadow-sm h-10 w-10"
              >
                <List className="h-5 w-5" />
              </Button>
              <Button
                className="bg-error-500 hover:bg-error-600 text-white font-semibold shadow-sm px-6 py-2 h-10 text-sm rounded-md"
                onClick={handleCreateChatbot}
              >
                {t('common.create')}
              </Button>
            </div>
          </section>

          <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredChatbots.length > 0 ? (
              filteredChatbots.map(bot => (
                <ChatbotCard
                  key={bot.id}
                  {...bot}
                  onDelete={handleDeleteChatbot}
                  onClone={handleCloneChatbot}
                  navigate={navigate}
                />
              ))
            ) : (
              <p className="col-span-full text-center text-tertiary-500 py-10">
                {t('home.noResults')}
              </p>
            )}
          </section>
        </div>
      </div>
    </MicroFrontendWrapper>
  );
};

export default function HomeWrapper({navigate}: HomeProps) {
  return (
    <Provider store={store}>
      <Home navigate={navigate}/>
    </Provider>
  );
}
