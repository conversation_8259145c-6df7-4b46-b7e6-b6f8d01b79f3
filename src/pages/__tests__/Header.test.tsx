import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import userEvent from '@testing-library/user-event';
import Header from '../NeuratalkBuilder/header';
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';
import { render } from '@/test/utils';
import { useTranslation } from 'react-i18next';

// Mock API calls
vi.mock('@/store/api/languageApi', () => ({
  useGetBotLanguagesQuery: vi.fn(() => ({
    data: {
      data: {
        items: [{ langId: 'en' }],
      },
    },
  })),
  useGetLanguagesQuery: vi.fn(() => ({
    data: {
      data: {
        items: [{ id: 'en', name: 'English', code: 'en' }],
      },
    },
  })),
}));

// Mock useTranslation
vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => {
        if (key === 'editor.domain') return 'Domain';
        if (key === 'common.selectOption') return 'Select option';
        if (key === 'domains.telecom') return 'Telecom';
        return key;
      },
    }),
  };
});

// Mock Redux hooks and actions
let currentChatbotState = {
  title: 'Test Chatbot',
  domain: 'ecomm',
  description: 'A test chatbot description',
};

vi.mock('@/hooks/useRedux', () => ({
  useAppSelector: vi.fn(selector => selector({ chatbot: { ...currentChatbotState } })),
  useAppDispatch: () => vi.fn(),
}));

vi.mock('@/store/slices/chatbotSlice', () => ({
  updateTitle: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, title: payload };
    return { type: 'chatbot/updateTitle', payload };
  }),
  updateDomain: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, domain: payload };
    return { type: 'chatbot/updateDomain', payload };
  }),
  updateDescription: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, description: payload };
    return { type: 'chatbot/updateDescription', payload };
  }),
}));

// Mock SVG import
vi.mock('../../../public/Group_24426.svg', () => ({ default: 'mocked-icon.svg' }));

// Mock createObjectURL for file upload
beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => 'blob:mock-url');
});

afterAll(() => {
  vi.restoreAllMocks();
});

describe.skip('Header', () => {
  it('renders chatbot title, domain, and description', async () => {
    render(<Header />);
    expect(screen.getByTestId('title')).toHaveTextContent('Test Chatbot');
    expect(screen.getByTestId('domain')).toHaveTextContent('ecomm');
    expect(screen.getByTestId('description')).toHaveTextContent('A test chatbot description');
    expect(screen.getByTestId('default-icon')).toBeInTheDocument();
  });

  it('displays the edit dialog with all form fields when edit is clicked', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const titleInput = await screen.findByTestId('title-input');
    const descriptionTextarea = await screen.findByTestId('description-textarea');

    expect(titleInput).toBeVisible();
    expect(descriptionTextarea).toBeVisible();
  });

  it('disables SAVE button if fields are not changed or incomplete', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const saveButton = await screen.findByTestId('save-button');
    expect(saveButton).toBeDisabled();
  });

  it('handles image upload with valid file', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const file = new File(['dummy'], 'test.png', { type: 'image/png' });
    const input = await screen.findByTestId('image-upload-input');
    await userEvent.upload(input, file);
  });

  it('removes selected image', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const file = new File(['dummy'], 'test.png', { type: 'image/png' });
    const input = await screen.findByTestId('image-upload-input');
    await userEvent.upload(input, file);

    await waitFor(() => {
      expect(screen.getByTestId('image-preview')).toBeInTheDocument();
    });

    const removeBtn = await screen.findByTestId('remove-image-button');
    await userEvent.click(removeBtn);

    await waitFor(() => {
      expect(screen.queryByTestId('image-preview')).not.toBeInTheDocument();
    });
  });

  it('closes dialog when CANCEL is clicked', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const cancelButton = await screen.findByTestId('cancel-button');
    await userEvent.click(cancelButton);
    await waitFor(() => {
      expect(screen.queryByTestId('title-input')).not.toBeInTheDocument();
    });
  });

  it('enables SAVE button when form is valid', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const titleInput = await screen.findByTestId('title-input');
    const descriptionTextarea = await screen.findByTestId('description-textarea');
    const saveButton = await screen.findByTestId('save-button');

    // Initially, the button is disabled
    expect(saveButton).toBeDisabled();

    // Clear existing value and fill out the form
    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'New Title');
    await waitFor(() => expect(titleInput).toHaveValue('New Title'));

    const domainSelectElement = await screen.findByLabelText('Domain');
    // Wait for the domain select to be enabled before interacting with it
    await waitFor(() => expect(domainSelectElement).toBeEnabled());

    await userEvent.click(domainSelectElement); // Open the select dropdown

    const telecomOption = await waitFor(() => screen.findByRole('option', { name: 'Telecom' })); // Find the option by its label
    await userEvent.click(telecomOption); // Click the option

    await waitFor(() => expect(screen.getByRole('combobox', { name: 'Telecom' })).toBeInTheDocument());

    await userEvent.clear(descriptionTextarea);
    await userEvent.type(descriptionTextarea, 'New Description');
    await waitFor(() => expect(descriptionTextarea).toHaveValue('New Description'));

    // The button should now be enabled
    expect(saveButton).toBeEnabled();
  });
});