import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import HomeWrapper from '../Home/Home';
import { Provider } from 'react-redux';
import { store } from '@/store/store';
import { describe, expect, it } from 'vitest';

// Helper to render with Redux provider
const renderWithProvider = (ui: React.ReactElement) =>
  render(
    <Provider store={store}>
      <MemoryRouter>{ui}</MemoryRouter>
    </Provider>
  );

describe('Home Page', () => {
  it('renders NeuraTalk AI description', () => {
    renderWithProvider(<HomeWrapper />);
    expect(screen.getByText(/NeuraTalk AI/i)).toBeInTheDocument();
    expect(screen.getByText(/cutting-edge conversational AI solution/i)).toBeInTheDocument();
  });

  it('renders search input', () => {
    renderWithProvider(<HomeWrapper />);
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  it('renders CREATE button', () => {
    renderWithProvider(<HomeWrapper />);
    expect(screen.getByText('CREATE')).toBeInTheDocument();
  });

  it('shows "No chatbots found" when no chatbots match search', () => {
    renderWithProvider(<HomeWrapper />);
    const input = screen.getByPlaceholderText('Search');
    fireEvent.change(input, { target: { value: 'nonexistentbot' } });
    expect(screen.getByText(/No chatbots found matching your search/i)).toBeInTheDocument();
  });
});
