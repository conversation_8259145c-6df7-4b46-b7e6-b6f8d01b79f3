import React from 'react';
import { render, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import PreviewModal from '../preview-modal';

// Mock Redux hooks globally
const mockDispatch = vi.fn();
const mockUseAppSelector = vi.fn();

vi.mock('../../hooks/useRedux', () => ({
  useAppDispatch: () => mockDispatch,
  useAppSelector: () => mockUseAppSelector(),
}));

describe('PreviewModal', () => {
  beforeEach(() => {
    // Mock the Redux selector to control `showPreview` state
    mockUseAppSelector.mockReturnValue({ showPreview: true });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('renders the modal when showPreview is true', () => {
    render(<PreviewModal />);
    expect(screen.getByText('Preview')).toBeInTheDocument();
  });

  it('does not render the modal when showPreview is false', () => {
    mockUseAppSelector.mockReturnValue({ showPreview: false });
    render(<PreviewModal />);
    expect(screen.queryByText('Preview')).not.toBeInTheDocument();
  });
});
