import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ert<PERSON>ircle, Info } from "lucide-react"
import { useAppDispatch, useAppSelector } from "../hooks/useRedux"
import { toggleDebugger } from "../store/slices/uiSlice"
import { useTranslation } from 'react-i18next'

export default function DebuggerPanel() {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const { showDebugger } = useAppSelector((state) => state.ui)

  const logs = [
    {
      type: "error",
      message: "Failed to load resource: the server responded with a status of 403 (Forbidden)",
      url: "analytics.google.com_eu&_ss=1&tfd=1046:1",
      icon: AlertCircle,
    },
    {
      type: "error",
      message: "Failed to load resource: the server responded with a status of 403 (Forbidden)",
      url: "analytics.google.com_90&_et=7&tfd=6062:1",
      icon: AlertCircle,
    },
    {
      type: "warning",
      message:
        "Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.",
      icon: AlertTriangle,
    },
    {
      type: "warning",
      message:
        "Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.",
      icon: AlertTriangle,
    },
  ]

  if (!showDebugger) return null

  return (
    <div className="h-80 bg-white border-t border-secondary-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-secondary-200">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-secondary-900">{t('common.debugger')}</h3>
          <button
            onClick={() => dispatch(toggleDebugger())}
            className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <div className="flex space-x-6 mt-3">
          <button className="text-sm font-medium text-primary-600 border-b-2 border-primary-600 pb-1">{t('common.console')}</button>
          <button className="text-sm font-medium text-secondary-500 hover:text-secondary-700 pb-1">{t('common.debug')}</button>
        </div>
      </div>

      {/* Console Content */}
      <div className="flex-1 overflow-y-auto bg-secondary-50">
        {logs.map((log, index) => {
          const Icon = log.icon
          return (
            <div
              key={index}
              className={`flex items-start space-x-3 px-4 py-3 border-b border-secondary-100 ${
                log.type === "error" ? "bg-error-50" : "bg-warning-50"
              }`}
            >
              <Icon
                className={`w-4 h-4 mt-0.5 flex-shrink-0 ${log.type === "error" ? "text-error-500" : "text-warning-500"}`}
              />
              <div className="flex-1 min-w-0">
                <div className={`text-sm font-mono ${log.type === "error" ? "text-error-700" : "text-warning-700"}`}>
                  {log.message}
                </div>
                {log.url && (
                  <div className="text-primary-600 underline text-xs mt-1 font-mono break-all">{log.url}</div>
                )}
              </div>
              <button className="text-secondary-400 hover:text-secondary-600 flex-shrink-0">
                <Info className="w-4 h-4" />
              </button>
            </div>
          )
        })}
      </div>
    </div>
  )
}
