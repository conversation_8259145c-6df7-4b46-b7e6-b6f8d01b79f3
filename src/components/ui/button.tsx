import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        solid: '',
        outline: 'border',
        ghost: 'bg-transparent',
        link: 'bg-transparent underline-offset-4 hover:underline px-0',
      },
      variantColor: {
        primary: 'text-primary-foreground bg-primary hover:bg-primary-700 border-primary',
        tertiary: 'text-tertiary-foreground bg-tertiary hover:bg-tertiary-700 border-tertiary',
        destructive:
          'text-destructive-foreground bg-destructive hover:bg-destructive/90 border-destructive',
        success: 'text-white bg-success-600 hover:bg-success-700 border-success-600',
        warning: 'text-white bg-warning-500 hover:bg-warning-600 border-warning-500',
      },
      size: {
        default: 'py-2 px-4 text-sm',
        sm: 'py-1 px-3 text-xs',
        lg: 'py-3 px-6 text-lg',
        icon: 'h-10 w-10 p-0',
      },
    },
    compoundVariants: [
      {
        variant: 'outline',
        variantColor: 'primary',
        className: 'bg-transparent text-primary hover:bg-primary/10 border-primary',
      },
      {
        variant: 'outline',
        variantColor: 'tertiary',
        className: 'bg-transparent text-tertiary-500 hover:bg-tertiary/10 border-tertiary-300',
      },
      {
        variant: 'outline',
        variantColor: 'destructive',
        className: 'bg-transparent text-destructive hover:bg-destructive/10 border-destructive',
      },
      {
        variant: 'outline',
        variantColor: 'success',
        className: 'bg-transparent text-success-600 hover:bg-success-600/10 border-success-600',
      },
      {
        variant: 'outline',
        variantColor: 'warning',
        className: 'bg-transparent text-warning-500 hover:bg-warning-500/10 border-warning-500',
      },
      {
        variant: 'ghost',
        variantColor: 'primary',
        className: 'hover:bg-primary/10 text-primary !bg-transparent',
      },
      {
        variant: 'ghost',
        variantColor: 'tertiary',
        className: 'hover:bg-tertiary/10 text-tertiary',
      },
      {
        variant: 'ghost',
        variantColor: 'destructive',
        className: 'hover:bg-destructive/10 text-destructive !bg-transparent',
      },
      {
        variant: 'ghost',
        variantColor: 'success',
        className: 'hover:bg-success-600/10 text-success-600 !bg-transparent',
      },
      {
        variant: 'ghost',
        variantColor: 'warning',
        className: 'hover:bg-warning-500/10 text-warning-500 !bg-transparent',
      },
    ],
    defaultVariants: {
      variant: 'solid',
      variantColor: 'primary',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, variantColor, size, asChild = false, children, disabled, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, variantColor, size }), className)}
        ref={ref}
        disabled={disabled}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
