import React, { useState, useRef, useEffect } from 'react';
import { X, ExternalLink, BugOff, Smile, Send } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { PlatformType } from '../lib/enums';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from './ui/select';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

interface ChatMessage {
  sender: 'user' | 'bot';
  nodeType: 'user_message' | 'message' | 'form';
  data: any;
  conversationId?: string;
}

const API_URL =
  'http://localhost:3001/api/v1/conversations/b537c064-2380-46a9-ac43-6aa2836dd618/message';
const botId = 'c835572e-3db5-419e-81e5-a53430deabeb';
const USER_ID = 'user123';
const CHANNEL = 'web';

export default function PreviewModal() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { showPreview } = useAppSelector(state => state.ui);

  const [platform, setPlatform] = React.useState<PlatformType>(PlatformType.Web);

  // Chat state
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const chatEndRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, showPreview]);

  if (!showPreview) return null;

  // Send message to API and update chat
  const sendMessage = async (content: string) => {
    if (!content.trim()) return;
    setError(null);

    // Add user message
    setMessages(prev => [
      ...prev,
      { data: { text: content }, nodeType: 'user_message', sender: 'user' },
    ]);
    setLoading(true);

    try {
      const res = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Bot-Id': botId,
        },
        body: JSON.stringify({
          content,
          messageType: 'text',
          botId,
          metadata: {
            userId: USER_ID,
            channel: CHANNEL,
          },
        }),
      });

      if (!res.ok) throw new Error(t('errors.failedToSend'));
      const data = await res.json();

      let botMessages: ChatMessage[] = [];

      if (data.success) {
        const responseData = data.data;

        // Handle wrapped response with array
        botMessages = responseData.response.map((msg: any) => ({
          sender: 'bot',
          nodeType: msg.nodeType,
          data: msg.data,
        }));
      } else {
        setError(t('errors.unexpectedResponse'));
      }

      if (botMessages.length > 0) {
        setMessages(prev => [...prev, ...botMessages]);
      }
    } catch (err: any) {
      setError(err.message || t('errors.somethingWrong'));
    } finally {
      setLoading(false);
    }
  };

  // Handle quick reply buttons
  const handleQuickReply = (text: string) => {
    setInput('');
    sendMessage(text);
  };

  // Handle input submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;
    sendMessage(input.trim());
    setInput('');
  };

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm text-tertiary-600">{t('common.preview')}</h3>
            <div className="flex items-center space-x-2">
              <div className="relative min-w-32">
                <Select value={platform} onValueChange={setPlatform}>
                  <SelectTrigger
                    className="
                      px-4 py-1.5
                      border-none
                      rounded-full
                      text-sm
                      bg-secondary-50
                      min-w-24
                      shadow-none
                      focus:ring-0 focus:outline-none
                      flex items-center justify-center
                      gap-1
                      h-auto
                    "
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(PlatformType).map(value => (
                      <SelectItem key={value} value={value}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <button className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center">
                <ExternalLink className="w-4 h-4" />
              </button>
              <button
                className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
                // onClick={() => dispatch(toggleDebugger())}
              >
                <BugOff className="w-4 h-4" />
              </button>
              <button
                onClick={() => dispatch(togglePreview())}
                className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Chat Interface */}
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col">
            {/* Chat Content */}
            <div className="flex-1 border-none p-4 space-y-3 overflow-y-auto">
              {messages.map((msg, idx) => {
                let content = '';
                if (msg.nodeType === 'user_message') {
                  content = msg.data.text;
                } else if (msg.nodeType === 'message') {
                  content = msg.data.text;
                } else if (msg.nodeType === 'form') {
                  content = msg.data.prompt?.map((p: any) => p.label).join('<br/>') || '';
                }

                return (
                  <div
                    key={idx}
                    className={cn('flex', msg.sender === 'user' ? 'justify-end' : 'justify-start')}
                  >
                    <div
                      className={cn(
                        'max-w-[80%] px-4 py-2 rounded-2xl shadow-sm text-base',
                        msg.sender === 'user'
                          ? 'bg-primary-100 text-primary-900 rounded-br-md'
                          : 'bg-tertiary-100 text-secondary-900 rounded-bl-md'
                      )}
                      dangerouslySetInnerHTML={{
                        __html: content.replace(/\n/g, '<br/>').replace(/\n/g, '<br/>'),
                      }}
                    ></div>
                  </div>
                );
              })}
              <div ref={chatEndRef} />
              {loading && (
                <div className="flex justify-start">
                  <div className="px-4 py-2 rounded-2xl bg-tertiary-100 text-secondary-400 text-base animate-pulse">
                    {t('form.typing')}
                  </div>
                </div>
              )}
              {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
            </div>

            {/* Quick Replies */}
            {/* <div className="flex flex-wrap gap-2 px-4 pb-2">
              <button
                className="px-3 py-2 text-primary-600 rounded-xl text-sm hover:bg-primary-100 transition-colors border border-primary-200"
                onClick={() => handleQuickReply('Track my order')}
                disabled={loading}
              >
                Track my order
              </button>
              <button
                className="px-3 py-2 text-primary-600 rounded-xl text-sm hover:bg-primary-100 transition-colors border border-primary-200"
                onClick={() => handleQuickReply('Cancel my order')}
                disabled={loading}
              >
                Cancel my order
              </button>
              <button
                className="px-3 py-2 text-primary-600 rounded-xl text-sm hover:bg-primary-100 transition-colors border border-primary-200"
                onClick={() => handleQuickReply('Chat with an Agent')}
                disabled={loading}
              >
                Chat with an Agent
              </button>
              <button
                className="px-3 py-2 text-primary-600 rounded-xl text-sm hover:bg-primary-100 transition-colors border border-primary-200"
                onClick={() => handleQuickReply('View similar products')}
                disabled={loading}
              >
                View similar products
              </button>
            </div> */}

            {/* Chat Input */}
            <form
              className="p-4 border-t border-secondary-200"
              onSubmit={handleSubmit}
              autoComplete="off"
            >
              <div className="flex items-center space-x-2 rounded-lg border border-secondary-300 px-1 py-1 focus-within:ring-2 focus-within:ring-primary-500">
                <input
                  type="text"
                  placeholder={t('common.typeMessage')}
                  className="flex-1 border-none px-2 text-sm bg-transparent focus:outline-none"
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  disabled={loading}
                  aria-label="Type your message"
                />
                <button
                  type="button"
                  className="w-9 h-9 rounded-lg bg-primary-50 text-primary-600 flex items-center justify-center hover:bg-primary-100 transition-colors"
                  tabIndex={-1}
                  disabled
                >
                  <Smile className="w-6 h-6" />
                </button>
                <button
                  type="submit"
                  className="w-8 h-8 bg-primary-600 text-white rounded-lg flex items-center justify-center hover:bg-primary-700 transition-colors disabled:opacity-50"
                  disabled={loading || !input.trim()}
                  aria-label="Send"
                >
                  <Send className="w-6 h-6 p-0.5" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
