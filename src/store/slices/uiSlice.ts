import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

interface UIState {
  activeView: string
  activeTab: string
  showPreview: boolean
  showMessage: boolean
  showDebugger: boolean
  isEditingTitle: boolean
  selectedFlowDropdown: string | null
}

const initialState: UIState = {
  activeView: "neuratalk",
  activeTab: "Design",
  showPreview: false,
  showMessage: false,
  showDebugger: false,
  isEditingTitle: false,
  selectedFlowDropdown: null,
}

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    setActiveView: (state, action: PayloadAction<string>) => {
      state.activeView = action.payload
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload
    },
    togglePreview: (state) => {
      state.showPreview = !state.showPreview
    },
    toggleMessageNode: (state) => {
      state.showMessage = !state.showMessage
    },
    toggleDebugger: (state) => {
      state.showDebugger = !state.showDebugger
    },
    setEditingTitle: (state, action: PayloadAction<boolean>) => {
      state.isEditingTitle = action.payload
    },
    setSelectedFlowDropdown: (state, action: PayloadAction<string | null>) => {
      state.selectedFlowDropdown = action.payload
    },
  },
})

export const { setActiveView, setActiveTab, togglePreview,toggleMessageNode, toggleDebugger, setEditingTitle, setSelectedFlowDropdown } =
  uiSlice.actions
export default uiSlice.reducer
