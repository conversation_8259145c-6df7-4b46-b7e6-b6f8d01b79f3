# Internationalization (i18n) Guide

This project uses `react-i18next` for internationalization support.

## Setup

The i18n configuration is automatically initialized when the app starts via `src/bootstrap.tsx`.

## Usage

### Basic Translation

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return <h1>{t('common.title')}</h1>;
}
```

### Translation with Variables

```tsx
const { t } = useTranslation();

// In translation file: "welcome": "Welcome, {{name}}!"
return <p>{t('welcome', { name: '<PERSON>' })}</p>;
```

### Using Custom Hook

```tsx
import { useI18n } from '@/hooks/useI18n';

function MyComponent() {
  const { t, changeLanguage, getCurrentLanguage } = useI18n();
  
  return (
    <div>
      <p>{t('common.hello')}</p>
      <button onClick={() => changeLanguage('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

## Language Switcher

Use the `LanguageSwitcher` component to allow users to change languages:

```tsx
import { LanguageSwitcher } from '@/components/LanguageSwitcher';

function Header() {
  return (
    <header>
      <LanguageSwitcher />
    </header>
  );
}
```

## Adding New Languages

1. Create a new translation file in `src/i18n/locales/[language-code].json`
2. Add the language to `src/i18n/config.ts`:
   ```ts
   import newLang from './locales/[language-code].json';
   
   const resources = {
     en: { translation: en },
     es: { translation: es },
     [languageCode]: { translation: newLang },
   };
   ```
3. Add the language option to `src/components/LanguageSwitcher.tsx`

## Translation File Structure

Translation files are organized by feature/section:

```json
{
  "common": {
    "save": "Save",
    "cancel": "Cancel"
  },
  "home": {
    "title": "Welcome",
    "description": "This is the home page"
  },
  "chatbot": {
    "create": "Create Chatbot",
    "delete": "Delete Chatbot"
  }
}
```

## Best Practices

1. **Namespace your keys**: Use nested objects to organize translations by feature
2. **Use descriptive keys**: `button.save` is better than `btn1`
3. **Keep translations consistent**: Use the same terminology across the app
4. **Handle pluralization**: Use i18next's pluralization features for countable items
5. **Test all languages**: Ensure UI doesn't break with longer/shorter text

## Available Languages

- English (en) - Default
- Spanish (es)

## Supported Features

- ✅ Basic text translation
- ✅ Variable interpolation
- ✅ Language switching
- ✅ Fallback to default language
- ✅ Namespace organization
- ⚠️ Pluralization (can be added if needed)
- ⚠️ RTL support (can be added if needed)