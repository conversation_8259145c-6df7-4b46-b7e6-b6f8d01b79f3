{"common": {"search": "Buscar", "filter": "Filtrar", "create": "CREAR", "save": "GUARDAR", "cancel": "CANCELAR", "delete": "Eliminar", "add": "AÑADIR", "clone": "Clonar", "export": "Exportar", "edit": "<PERSON><PERSON>", "yes": "SÍ", "no": "NO", "selectOption": "Seleccionar opción", "getStarted": "EMPEZAR", "preview": "Vista previa", "publish": "PUBLICAR", "duplicate": "Duplicar", "versionHistory": "Historial de versiones", "flows": "<PERSON><PERSON><PERSON>", "console": "Consola", "debug": "<PERSON><PERSON><PERSON>", "debugger": "Depurador", "message": "Men<PERSON><PERSON>", "image": "Imagen", "file": "Archivo", "video": "Video", "addViaUrl": "<PERSON><PERSON><PERSON>", "enterFileUrl": "Introducir URL del archivo", "maxSize": "<PERSON><PERSON><PERSON> máximo: {{size}}MB", "clickOrDrag": "Haga clic o arrastre el archivo {{type}} aquí", "clickOrDragFiles": "Haga clic o arrastre archivo{{plural}} aquí (imágenes, vídeos, documentos)", "writeMessage": "Escribe un mensaje", "typeMessage": "Escribe tu mensaje...", "trackOrder": "Rast<PERSON>r mi pedido", "cancelOrder": "Cancelar mi pedido", "chatWithAgent": "Chatear con un agente", "viewSimilarProducts": "Ver productos similares", "hello": "¡<PERSON><PERSON>, {{name}}!", "howCanIHelp": "¿En qué puedo ayudarte hoy?", "searchFlows": "Buscar flujos...", "onboarding": "Introducción", "notFound": "No encontrado", "enterValidValue": "Por favor ingresa un valor válido", "translateTo": "Traducir a", "translate": "TRADUCIR", "nothingToShow": "Nada que mostrar", "generate": "Generar", "close": "<PERSON><PERSON><PERSON>", "nodeId": "ID del nodo:", "noData": "Sin datos", "searchEllipsis": "Buscar...", "justNow": "justo ahora", "update": "Actualizar"}, "home": {"title": "NeuraTalk IA", "description": "es una solución de IA conversacional de vanguardia diseñada para mejorar el compromiso con el cliente, automatizar el soporte y optimizar las operaciones comerciales.", "noResults": "No se encontraron chatbots que coincidan con tu búsqueda.", "lastUpdated": "Última actualización {{date}}"}, "chatbot": {"untitled": "Chatbot sin título", "noDomain": "Ningún dominio seleccionado", "noDescription": "Sin descripción", "confirmDelete": "CONFIRMAR ELIMINACIÓN", "deleteMessage": "¿Estás seguro de que deseas eliminar este chatbot?", "noCancel": "NO, CANCELAR", "yesDelete": "SÍ, ELIMINAR"}, "editor": {"chatbotName": "Nombre del chatbot", "domain": "<PERSON>inio", "description": "Descripción", "uploadImage": "Haz clic o arrastra un archivo a esta área para subir", "uploadFormat": "(Tamaño: hasta 5 MB | Formato: jpg, png)", "unsupportedFile": "Tipo de archivo no compatible", "fileTooLarge": "El archivo debe ser menor de 5 MB", "invalidImageFile": "<PERSON>r <PERSON>, sube un archivo de imagen válido (png, jpg, jpeg, webp, gif, svg)"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "<PERSON><PERSON><PERSON>"}, "domains": {"ecomm": "Comercio electrónico", "telecom": "Telecomunicaciones", "retail": "Retail", "travel": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "emptyState": {"title": "Nada por aquí aún", "description": "Actualmente no hay contenido para mostrar."}, "intents": {"title": "Intenciones", "addTitle": "AÑADIR INTENCIÓN", "nameLabel": "Nombre de la intención", "nameRequired": "El nombre de la intención es obligatorio.", "startAdding": "Comienza a añadir intenciones", "noFlowsConnected": "No hay flujos conectados", "selectToManage": "Selecciona una intención para gestionar las expresiones", "loading": "Cargando intenciones...", "loadingError": "Error al cargar intenciones.", "utterances": {"title": "Expresiones", "addTitle": "AÑADIR EXPRESIÓN", "editTitle": "EDITAR EXPRESIÓN", "enterPlaceholder": "Introduce la expresión", "startAdding": "Comienza a añadir expresiones", "emptyError": "La expresión no puede estar vacía.", "loading": "Cargando expresiones...", "loadingError": "Error al cargar expresiones.", "utteranceAdded": "Expresión añadida.", "utteranceUpdated": "Expresión actualizada."}}, "train": {"entities": {"title": "Entidades", "content": "Contenido de entidades", "addTitle": "AÑADIR ENTIDAD", "nameLabel": "Nombre de la entidad", "intentIdLabel": "ID de intención", "metadataLabel": "Metadatos (JSON)", "metadataPlaceholder": "Introduce metadatos en formato JSON", "loading": "Cargando entidades...", "error": "Error al cargar entidades.", "validation": {"nameRequired": "El nombre de la entidad es obligatorio.", "intentIdRequired": "El ID de intención es obligatorio.", "invalidJson": "Formato JSON no válido para metadatos."}}, "synonyms": {"title": "Sinónimos", "content": "Contenido de sinónimos"}, "smallTalk": {"title": "Conversación informal", "content": "Contenido de conversación informal"}, "trainFromLogs": {"title": "Entrenar desde registros", "content": "Contenido para entrenar desde registros"}, "tabs": {"intentUtterances": "Expresiones de intención", "entities": "Entidades", "faqs": "Preguntas frecuentes", "synonyms": "Sinónimos", "smallTalk": "Conversación informal", "trainFromLogs": "Entrenar desde registros"}}, "faqs": {"title": "Preguntas y respuestas", "category": {"title": "Categoría", "addTitle": "AÑADIR CATEGORÍA", "nameLabel": "Nombre de la categoría", "startAdding": "Comienza a añadir categorías", "selectToManage": "Selecciona una categoría para gestionar preguntas", "nameRequired": "El nombre de la categoría es obligatorio."}, "loading": "Cargando preguntas frecuentes...", "loadingError": "Error al cargar preguntas frecuentes.", "items": {"loading": "Cargando elementos de preguntas frecuentes...", "loadingError": "Error al cargar elementos de preguntas frecuentes.", "startAdding": "Comienza a añadir preguntas", "addTitle": "AÑADIR PREGUNTA", "editTitle": "EDITAR PREGUNTA", "questionLabel": "Preguntas", "questionPlaceholder": "Introduce la pregunta", "questionEmpty": "La pregunta no puede estar vacía.", "atLeastOne": "Se requiere al menos una pregunta.", "answerLabel": "Respuesta", "answerPlaceholder": "Introduce la respuesta", "answerEmpty": "La respuesta no puede estar vacía.", "linkFlowLabel": "Vincular flujo", "chooseFlowPlaceholder": "Elige un flujo", "primaryLabel": "Principal", "questionPrefix": "P", "answerPrefix": "R", "questionsAdded": "Preguntas añadidas.", "questionsUpdated": "Preguntas actualizadas."}}, "fileUpload": {"fileTooLarge": "El archivo debe ser menor de {{size}} MB y del tipo {{type}}", "someFilesRejected": "Algunos archivos fueron rechazados. Asegúrate de que sean correctos y menores de {{size}} MB.", "failedToUpload": "Error al subir: {{filename}}"}, "tabs": {"contentComingSoon": "Contenido para la pestaña {{tabName}} próximamente"}, "builder": {"tabs": {"design": "Diseño", "train": "Entrenamiento", "channels": "Canales", "agentTransfer": "Transferencia a agente", "integrations": "Integraciones", "settings": "Configuración"}}, "flows": {"untitledFlow": "flujo sin título", "welcome": "Bienvenida", "fallback": "Recurso"}, "settings": {"language": "Idioma", "nlu": "NLU", "personalization": "Personalización", "llmConfiguration": "Configuración LLM", "cannedResponses": "Respuestas rápidas", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor"}, "stencil": {"nodes": "Nodos", "searchNodes": "Buscar nodos..."}, "platform": {"web": "Web", "mobile": "Móvil"}, "pagination": {"previous": "Anterior", "next": "Siguient<PERSON>", "morePages": "<PERSON>ás páginas"}, "form": {"loadingForm": "Cargando formulario...", "typing": "Escribiendo..."}, "errors": {"failedToSend": "Error al enviar el mensaje", "unexpectedResponse": "Respuesta inesperada del servidor", "somethingWrong": "Algo salió mal"}, "channels": {"selectWABA": "Selecciona un número WABA para conectar", "changeNumber": "CAMBIAR NÚMERO", "webhook": "Webhook", "webhookInstruction": "Pega este Webhook en el número WABA en el canal NGAGE WhatsApp para integrar.", "switchToMeta": "Cambiar a Meta Cloud API", "switchDescription": "Cambia a Meta Cloud API y vincula tu chatbot a través del BSP asociado.", "switch": "CAMBIAR", "connect": "CONECTAR", "selectChannels": "Selecciona canales para configurar", "nothingSelected": "<PERSON>da se<PERSON>", "myChannels": "Mis canales", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voz", "alexa": "Alexa", "available": "Disponible", "invalid": "INVÁLIDO", "testChannel": "Canal de prueba", "getStarted": "COMENZAR", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "filters": {"all": "Todos", "native": "Nativo", "text": "Texto", "voice": "Voz"}, "tabs": {"available": "Disponibles", "myChannels": "Mis canales"}}, "nodes": {"agentTransfer": "Transferencia a agente", "appEnd": "Fin de la app", "appStart": "Inicio de <PERSON>", "choice": "Opción", "choiceOption": "Opción de elección", "feedback": "Retroalimentación", "flowConnector": "<PERSON><PERSON> de <PERSON>", "http": "HTTP", "interactiveMessage": "Mensaje interactivo", "language": "Idioma", "message": "Men<PERSON><PERSON>", "notification": "Notificación", "payment": "Pago", "script": "<PERSON><PERSON><PERSON>", "text": "Texto", "waitDelay": "<PERSON><PERSON><PERSON> retardo", "whatsapp": "WhatsApp"}, "bots": {"testBot": "<PERSON><PERSON>", "testChatbot": "<PERSON><PERSON><PERSON>", "aChatbot": "Un chatbot de prueba", "aChatbotDescription": "Descripción de prueba del chatbot", "myFlow": "Mi flujo", "lastUpdatedToday": "Última actualización hoy"}, "whatsapp": {"onboarding": {"ngage": {"description": "Incorpora WABA usando el canal NGAGE WhatsApp e intégralo con tu chatbot."}, "meta": {"description": "Incorpora WABA usando Meta Cloud API y vincula tu chatbot mediante el BSP asociado."}}}}