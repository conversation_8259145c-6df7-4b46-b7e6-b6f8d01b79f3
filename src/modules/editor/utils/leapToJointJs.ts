import { v4 as uuidv4 } from "uuid";
import type { ModuleData, LeapModules, JointJSData } from "../types";
import { getNodeConstant, linksConstant } from "./jsonConstant";

export const leapToJointJSON = (modules: LeapModules): JointJSData => {
  const modulesKeys = Object.keys(modules);
  const cells: any[] = [];
  const jsonData: Record<string, ModuleData> = {};

  modulesKeys.forEach((id, index) => {
    const {
      type,
      coordinates: { x, y },
      output = {},
      settings,
      process,
    } = modules[id];

    const { customCodeIds = {} } = output || {};
    const { conditionalLink = [] }: any = customCodeIds || {};

    const cell = getNodeConstant(
      { type, name: settings.nodeName, image: settings?.image },
      { x, y },
      id,
      index + 1
    );

    jsonData[id] = {
      output,
      settings,
      process,
    };

    if (type === "choice") {
      const {
        process: { match_conditions, no_match_module_id },
      } = modules[id];

      const portId = uuidv4().slice(0, 7);
      const portId1 = uuidv4().slice(0, 7);
      const portInId = uuidv4().slice(0, 7);

      cell.ports = {
        ...cell.ports,
        items: [
          {
            id: portId,
            group: "out",
            args: { x: 12 },
            attrs: {
              portBody: {
                fill: "#31D88A",
                stroke: "#31D88A",
              },
            },
          },
          {
            id: portId1,
            group: "out",
            args: { x: 40 },
            attrs: {
              portBody: {
                fill: "red",
                stroke: "red",
              },
            },
          },
          {
            group: "in",
            id: portInId,
          },
        ],
      };

      match_conditions.forEach(({ id: optionId, ...rest }, index) => {
        if (!rest.key) {
          return null;
        }

        const {
          coordinates = {},
          condition = "",
          value = "",
        } = match_conditions[index - 1] ?? {};

        const maxLen = Math.max(condition.length, value.length);
        const prevNodeX = coordinates.x ?? x;
        const nodeX =
          index === 0 ? x + 150 * (index + 1) : prevNodeX + maxLen * 9 + 80;

        const positionDetails = rest.coordinates.x
          ? rest.coordinates
          : { x: nodeX, y: y + 100 };

        const choiceNodeCell = getNodeConstant(
          { type: "choiceOption" },
          positionDetails,
          optionId,
          index + 1,
          rest
        );

        const choiceOptionPortId = uuidv4().slice(0, 7);

        choiceNodeCell.ports = {
          ...choiceNodeCell.ports,
          items: [
            {
              group: "out",
              id: choiceOptionPortId,
            },
          ],
        };

        cells.push(choiceNodeCell);

        // Create link from choice to choice option
        const linksConstantCopy = { ...linksConstant };
        linksConstantCopy.source = {
          ...linksConstantCopy.source,
          id,
          port: portId,
        };
        linksConstantCopy.target = {
          ...linksConstantCopy.target,
          id: optionId,
        };
        linksConstantCopy.id = uuidv4().slice(0, 7);
        cells.push(linksConstantCopy);

        // Create link from choice option to next module
        if (rest.moduleId) {
          const linksConstantChoiceOption = { ...linksConstant };
          linksConstantChoiceOption.source = {
            ...linksConstantChoiceOption.source,
            id: optionId,
            port: choiceOptionPortId,
          };
          linksConstantChoiceOption.target = {
            ...linksConstantChoiceOption.target,
            id: rest.moduleId,
          };
          linksConstantChoiceOption.id = uuidv4().slice(0, 7);
          cells.push(linksConstantChoiceOption);
        }
      });

      // Handle no match condition
      if (no_match_module_id) {
        const linksConstantChoiceOption = { ...linksConstant };
        linksConstantChoiceOption.source = {
          ...linksConstantChoiceOption.source,
          id,
          port: portId1,
        };
        linksConstantChoiceOption.target = {
          ...linksConstantChoiceOption.target,
          id: no_match_module_id,
        };
        linksConstantChoiceOption.id = uuidv4().slice(0, 7);
        cells.push(linksConstantChoiceOption);
      }
    }

    // Handle conditional links
    if (conditionalLink.length) {
      const portId = uuidv4().slice(0, 7);
      const links = conditionalLink.map((targetId) => {
        const linksConstantCopy = { ...linksConstant };
        linksConstantCopy.source = {
          ...linksConstantCopy.source,
          id,
          port: portId,
        };
        linksConstantCopy.target = {
          ...linksConstantCopy.target,
          id: targetId,
        };
        linksConstantCopy.id = uuidv4().slice(0, 7);

        cell.ports = {
          ...cell.ports,
          items: [
            {
              group: "out",
              id: portId,
            },
            {
              group: "in",
              id: uuidv4().slice(0, 7),
            },
          ],
        };

        return JSON.parse(JSON.stringify(linksConstantCopy));
      });

      if (links.length) {
        cells.push(...links);
      }
    }

    cells.push(cell);
  });

  // Set target ports for links
  cells.forEach(({ type, id, target }, index) => {
    if (type === "standard.Link") {
      const nodeIndex = cells.findIndex(
        ({ id: nodeId }) => nodeId === target.id
      );

      const portInId =
        cells[nodeIndex]?.ports?.items?.find(({ group }) => group === "in")
          ?.id ?? uuidv4().slice(0, 7);

      if (nodeIndex !== -1) {
        cells[index].target = {
          ...cells[index].target,
          port: portInId,
        };

        cells[nodeIndex].ports.items = cells[nodeIndex].ports.items.filter(
          ({ group }) => group !== "in"
        );

        cells[nodeIndex].ports.items.push({
          group: "in",
          id: portInId,
        });
      }
    }
  });

  return {
    nodes: { cells },
    jsonData,
  };
};
