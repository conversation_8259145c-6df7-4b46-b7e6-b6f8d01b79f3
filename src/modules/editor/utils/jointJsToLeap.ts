import { v4 as uuidv4 } from 'uuid';
import type { dia } from 'rappid';
import type { ModuleData, LeapModules, JointJSCell } from '../types';
import { getJsonConstant } from './jsonConstant';
import { getModuleText } from './config';
import { ExtendedStencilNodes } from './constants';

const getCustomCode = (id: string): string => {
  return `// Custom code panel
// main function is the default method executed after processing current module
function main(){
  return "${id}";// return end moduleId
}`;
};

export const joinToLeapJSON = (
  jointJson: { cells: JointJSCell[] },
  settingDetails: Record<string, ModuleData>,
  graph: dia.Graph
): { modules: LeapModules; startId: string | null } => {
  const modules: LeapModules = {};
  const linkNodes: JointJSCell[] = [];
  const idMapping: Record<string, string> = {};

  const choiceNodeUtils = {
    choiceNodeId: null as string | null,
    choicePositionDetails: {} as Record<string, { x: number; y: number }>,
  };

  const choiceNodesMap: string[] = [];
  let startId: string | null = null;

  // Process nodes first
  jointJson.cells.forEach(nodeDetails => {
    const { type, id } = nodeDetails;

    if (type === ExtendedStencilNodes.CHOICE_OPTION) {
      const uuid = id.length === 7 ? id : uuidv4().slice(0, 7);
      idMapping[id] = uuid;
      choiceNodeUtils.choicePositionDetails[uuid] = {
        ...nodeDetails.position,
      };
      return;
    }

    if (type !== 'standard.Link') {
      const { position } = nodeDetails;
      let leapJson = getJsonConstant(type);
      const nodeSetting = settingDetails[id];

      if (nodeSetting) {
        if (nodeSetting.output.customCodeIds) {
          nodeSetting.output.customCodeIds.conditionalLink = [];
        }
        leapJson = { ...leapJson, ...nodeSetting };
      }

      const uuid = id.length === 7 ? id : uuidv4().slice(0, 7);

      if (type === ExtendedStencilNodes.APP_START) {
        startId = uuid;
      }

      if (type === ExtendedStencilNodes.CHOICE) {
        choiceNodeUtils.choiceNodeId = uuid;
        choiceNodesMap.push(uuid);

        leapJson.process.match_conditions = leapJson.process.match_conditions.map(d => ({
          ...d,
          moduleId: null,
        }));
      }

      leapJson.coordinates = {
        ...position,
        nodeData: {
          title: getModuleText(type),
          name: type,
          id: uuid,
          isEditable: true,
          canDelete: false,
          status: '',
          moduleType: type,
        },
      };

      idMapping[id] = uuid;
      modules[uuid] = { ...leapJson };
    } else {
      linkNodes.push(nodeDetails);
    }
  });

  // Process links
  linkNodes.forEach(linkDetails => {
    const {
      source: { id: sourceId },
      target: { id: targetId },
    } = linkDetails;

    const sourceModule = modules[idMapping[sourceId]];
    const targetModule = modules[idMapping[targetId]];

    const sourceType = sourceModule?.type || 'choiceOption';
    const targetType = targetModule?.type || 'choiceOption';

    // Mark target as linked to choice/script
    if (targetType === 'choice' && sourceModule) {
      sourceModule.isChoiceLinked = true;
    }

    if (targetType === 'script' && sourceModule) {
      sourceModule.isScriptLinked = true;
    }

    // Handle choice option connections
    if (sourceType === 'choiceOption') {
      const choiceNodeId = Object.keys(modules).find(nodeId => {
        const matchIndex = (modules[nodeId]?.process.match_conditions || []).findIndex(
          ({ id }) => id === sourceId
        );
        return modules[nodeId].type === 'choice' && matchIndex !== -1;
      });

      if (choiceNodeId) {
        const matchIndex = modules[choiceNodeId].process.match_conditions.findIndex(
          ({ id }) => id === sourceId
        );
        if (matchIndex !== -1) {
          modules[choiceNodeId].process.match_conditions[matchIndex].moduleId = targetId;
        }
      }
      return;
    }

    // Handle choice node connections
    if (sourceType === 'choice') {
      const source = graph.getCell(sourceId);
      const {
        attributes: {
          ports: { items },
        },
      } = source;

      const portDetails = items.find(({ id }) => id === linkDetails.source?.port);
      const {
        attrs: {
          portBody: { fill },
        },
      } = portDetails;

      if (fill === 'red') {
        modules[idMapping[sourceId]].process.no_match_module_id = targetId;
      }
      return;
    }

    // Handle regular connections
    if (idMapping[targetId] && sourceModule) {
      // Initialize conditionalLink if not exists
      if (!Array.isArray(sourceModule.output.customCodeIds?.conditionalLink)) {
        sourceModule.output.customCodeIds!.conditionalLink = [];
      }

      // Add target to conditional links
      sourceModule.output.customCodeIds?.conditionalLink.push(idMapping[targetId]);

      // Update custom code
      sourceModule.output.customCode = getCustomCode(idMapping[targetId]);
    }
  });

  // Add choice option positions
  if (choiceNodesMap.length) {
    choiceNodesMap.forEach(choiceId => {
      modules[choiceId].process.match_conditions = modules[choiceId].process.match_conditions.map(
        matchDetails => ({
          ...matchDetails,
          coordinates: {
            ...(choiceNodeUtils.choicePositionDetails[matchDetails.id] ?? {
              x: null,
              y: null,
            }),
          },
        })
      );
    });
  }

  // Clean up invalid references
  const nodeKeys = Object.keys(modules);

  nodeKeys.forEach(k => {
    const { type, process } = modules[k];

    if (type === 'choice') {
      const { no_match_module_id, match_conditions } = process;

      // Clean up no_match_module_id
      if (!nodeKeys.includes(no_match_module_id)) {
        modules[k].process.no_match_module_id = null;
      }

      // Clean up match_conditions
      match_conditions.forEach((d, i) => {
        const { moduleId } = d;
        if (!nodeKeys.includes(moduleId)) {
          modules[k].process.match_conditions[i].moduleId = null;
        }
      });
    }
  });

  return { modules, startId };
};
