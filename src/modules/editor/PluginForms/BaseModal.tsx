import React, {
  useState,
  useEffect,
  ReactNode,
  ChangeEvent,
  FormEvent,
  KeyboardEvent,
} from 'react';
import CloseIcon from '@mui/icons-material/Close';
import copyIcon from '../assets/common/pluginIcons/copy.svg';
import editIcon from '../assets/common/pluginIcons/edit.svg';
import { getFormSchema } from './utils/schema';
import { getModuleIcon, getModuleText } from '../utils/config';
import { useTranslation } from 'react-i18next';
import { ModuleData } from '../types';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { useForm, useFormContext } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import './style.css';
import { StencilNodesType } from '../utils/constants';
import { Form } from '@/components/ui/form';

interface Position {
  top: number;
  left: number;
}

interface BaseModalProps {
  id: string;
  position: Position;
  type: StencilNodesType;
  moduleData: ModuleData;
  startUrl?: string;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
  handleClose: () => void;
  handleSave: (data: ModuleData, id: string, validate?: boolean) => void;
  children: ReactNode;
}

const BaseModal: React.FC<BaseModalProps> = ({
  id,
  position: { top, left },
  type,
  moduleData,
  startUrl,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { schema } = getFormSchema(type);
  const form = useForm<ModuleData>({
    resolver: zodResolver(schema as z.ZodSchema<ModuleData>),
    defaultValues: moduleData,
  });

  return (
    <Form {...form}>
      <BaseModalContent
        id={id}
        position={{ top, left }}
        type={type}
        startUrl={startUrl}
        isEdit={isEdit}
        isPublishedEnabled={isPublishedEnabled}
        handleClose={handleClose}
        handleSave={handleSave}
      >
        {children}
      </BaseModalContent>
    </Form>
  );
};

interface BaseModalContentProps extends Omit<BaseModalProps, 'moduleData' | 'children'> {
  children: ReactNode;
}

const hideSubmit = ['choice', 'whatsapp'];

const BaseModalContent: React.FC<BaseModalContentProps> = ({
  id,
  type,
  startUrl,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { t } = useTranslation();
  const form = useFormContext<ModuleData>();
  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = form;

  const [isEditable, setIsEditable] = useState(false);

  const nodeName = watch('settings.nodeName');

  useEffect(() => {
    if (isPublishedEnabled && Object.keys(errors).length === 0) {
      handleSubmit(data => handleSave(data, id, true))();
    }
  }, [isPublishedEnabled, errors, handleSubmit, handleSave, id]);

  const handleNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    setValue('settings.nodeName', e.target.value);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && nodeName) {
      setIsEditable(false);
      handleSubmit(data => handleSave(data, id, false))();
    }
  };

  const submitForm = async (e: FormEvent) => {
    e.preventDefault();
    handleSubmit(data => {
      handleSave(data, id);
      handleClose();
    })();
  };

  const childrenWithProps = React.Children.map(
    children,
    child => (React.isValidElement(child) ? React.cloneElement(child, {}) : child) //TODO: if required than pass it through props
  );

  return (
    <Sheet open={!!type}>
      <SheetContent hideOverlay side="left" className="w-96 p-0 flex flex-col">
        <form
          id="setting-form"
          onSubmit={submitForm}
          className={`relative flex flex-col bg-background overflow-hidden h-full`}
        >
          {/* Close Button */}
          <div className="mt-3">
            <button onClick={handleClose} className="absolute top-6 right-3 text-gray-400">
              <CloseIcon />
            </button>

            <h2 className="text-xs pl-3">
              <span className="text-gray-400">Node ID: </span>
              <span className="text-gray-600">{id}</span>
            </h2>

            {/* Header */}
            <div className="flex mt-3 pl-3 mb-3">
              {isEditable || (nodeName && nodeName.length > 25) ? (
                <input
                  type="text"
                  value={nodeName || ''}
                  onChange={handleNameChange}
                  onKeyDown={handleKeyDown}
                  style={{ height: '35px', width: '200px', borderRadius: '10px' }}
                />
              ) : (
                <>
                  <span className="mr-2">{getModuleText(type)}</span>
                  {isEdit && (
                    <button
                      onClick={() => setIsEditable(true)}
                      style={{ height: '20px', width: '20px' }}
                    >
                      <img src={editIcon} alt="Edit" style={{ height: '12px', width: '12px' }} />
                    </button>
                  )}
                </>
              )}
            </div>

            <hr />
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto mt-4 space-y-4 pr-1">{childrenWithProps}</div>

          {/* Footer */}
          {!hideSubmit.includes(type) && isEdit && (
            <div className="flex justify-end gap-2 mb-6 pt-4 mr-4">
              <button
                type="button"
                onClick={handleClose}
                className="border px-6 py-1 h-10 w-28 rounded-sm"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="bg-primary-500 text-white px-6 h-10 w-28 py-1 rounded-sm"
              >
                {t('common.save')}
              </button>
            </div>
          )}
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default BaseModal;
