import { useCallback, useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { useUpdateApplicationDetailsMutation } from '@/store/api/studioApi';
import type { ApplicationData } from '../../../types';

export const useAutoSave = (getSVGString: () => Promise<string>) => {
  const [autoUpdateLoader, setAutoUpdateLoader] = useState(false);
  const [updateApplicationDetails] = useUpdateApplicationDetailsMutation();

  useEffect(() => {
    const alertUser = (e: BeforeUnloadEvent) => {
      if (autoUpdateLoader) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', alertUser);
    return () => {
      window.removeEventListener('beforeunload', alertUser);
    };
  }, [autoUpdateLoader]);

  const autoUpdateJson = useCallback(
    debounce(async (payload: ApplicationData, id: string) => {
      try {
        setAutoUpdateLoader(true);
        const image = await getSVGString();
        const updatedPayload = { ...payload, svg: image };

        await updateApplicationDetails({
          appId: id,
          payload: updatedPayload,
        }).unwrap();

        setAutoUpdateLoader(false);
      } catch (error) {
        setAutoUpdateLoader(false);
      }
    }, 500),
    [getSVGString, updateApplicationDetails]
  );

  return {
    autoUpdateLoader,
    autoUpdateJson,
  };
};
