import React, { useState, useRef, useEffect } from 'react';
import {
  Plus,
  Search,
  ChevronsLeft,
  ChevronsRight,
  MoreVertical,
  X,
  Copy,
  FileClock,
  Trash,
} from 'lucide-react';
import { FlowsState } from '../types';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';

const initialState: FlowsState = {
  flows: [
    {
      id: 'welcome',
      name: 'Welcome',
      type: 'Default',
      nodes: [],
      connections: [],
      isActive: false,
    },
    {
      id: 'fallback',
      name: 'Fallback',
      type: 'Default',
      nodes: [],
      connections: [],
      isActive: false,
    },
  ],
  activeFlowId: 'welcome',
  loading: false,
  error: null,
};

export default function FlowsPanel() {
  const { t } = useTranslation();
  const [flows, setFlows] = useState(initialState.flows);
  const [activeFlowId, setActiveFlowId] = useState<string | null>(null);
  const [editingFlowId, setEditingFlowId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleFlowClick = (flowId: string) => {
    setActiveFlowId(flowId);
  };

  const handleEditFlow = (flowId: string, currentName: string) => {
    setEditingFlowId(flowId);
    setEditingName(currentName);
  };

  const handleSaveEdit = () => {
    if (editingFlowId) {
      setFlows(prevFlows =>
        prevFlows.map(flow => {
          if (flow.id === editingFlowId) {
            const newName = editingName.trim() !== '' ? editingName : flow.name;
            return { ...flow, name: newName };
          }
          return flow;
        })
      );
      setEditingFlowId(null);
      setEditingName('');
    }
  };

  const handleDeleteFlow = (flowId: string) => {
    setFlows(prevFlows => prevFlows.filter(flow => flow.id !== flowId));
  };

  const handleDuplicateFlow = (flowId: string) => {
    setFlows(prevFlows => {
      const flowToDuplicate = prevFlows.find(flow => flow.id === flowId);
      if (!flowToDuplicate) return prevFlows;

      const clonedFlows = prevFlows.filter(flow =>
        flow.name.startsWith(`${flowToDuplicate.name} Cloned`)
      );
      const numbers = clonedFlows
        .map(flow => {
          const match = flow.name.match(/^.* Cloned(\d*)$/);
          return match?.[1] ? parseInt(match[1]) : 0;
        })
        .filter(num => !isNaN(num));
      let nextNumber = 0;
      while (numbers.includes(nextNumber)) {
        nextNumber++;
      }
      const newFlowName =
        nextNumber === 0
          ? `${flowToDuplicate.name} Cloned`
          : `${flowToDuplicate.name} Cloned${nextNumber}`;

      return [
        {
          ...flowToDuplicate,
          id: Date.now().toString(),
          name: newFlowName,
          isActive: false,
        },
        ...prevFlows,
      ];
    });
  };

  const handleCreateFlow = () => {
    setFlows(prevFlows => {
      const untitledFlows = prevFlows.filter(flow => flow.name.startsWith('UntitledFlow'));

      const numbers = untitledFlows
        .map(flow => {
          const match = flow.name.match(/^UntitledFlow(\d*)$/);
          return match?.[1] ? parseInt(match[1]) : 0;
        })
        .filter(num => !isNaN(num));

      let nextNumber = 0;
      while (numbers.includes(nextNumber)) {
        nextNumber++;
      }

      const newFlowName = nextNumber === 0 ? t('flows.untitledFlow') : `${t('flows.untitledFlow')}${nextNumber}`;

      return [
        {
          id: Date.now().toString(),
          name: newFlowName,
          type: 'Custom',
          nodes: [],
          connections: [],
          isActive: false,
        },
        ...prevFlows,
      ];
    });
  };

  const handleSearchToggle = () => {
    setIsSearchActive(!isSearchActive);
    setSearchQuery('');
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={`absolute left-4 top-4 max-h-[60vh] bg-white rounded-lg shadow-floating flex flex-col overflow-hidden z-10 transition-all duration-300 ${
        isCollapsed ? 'w-22' : 'w-64'
      }`}
    >
      {/* Header */}
      <div className="p-4 border-b border-secondary-200">
        {isCollapsed ? (
          <div className="flex justify-between items-center">
            <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
            <button
              aria-label="expand-panel"
              onClick={handleToggleCollapse}
              className="w-5 h-5 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
            >
              <ChevronsRight className="w-4 h-4" />
            </button>
          </div>
        ) : isSearchActive ? (
          <div className="flex-1 transition-all duration-300 ease-in-out">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder={t('common.searchFlows')}
                className="w-full text-sm text-secondary-900 border border-secondary-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-1 focus:ring-primary-600"
                autoFocus
              />
              <button
                onClick={handleSearchToggle}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
              >
                <X className="w-6 h-6 font-bold text-black" />
              </button>
            </div>
          </div>
        ) : (
          <div className="flex justify-between items-center">
            <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
            <div className="flex items-center space-x-2">
              <button
                aria-label="add-flow"
                data-testid="add-flow-btn"
                onClick={handleCreateFlow}
                className="w-6 h-6 bg-primary-600 text-white rounded-md flex items-center justify-center hover:bg-primary-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
              </button>
              <button
                data-testid="search-btn"
                onClick={handleSearchToggle}
                className="w-8 h-8 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
              >
                <Search className="w-4 h-4" />
              </button>
              <button
                aria-label="collapse-panel"
                onClick={handleToggleCollapse}
                className="w-5 h-5 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
              >
                <ChevronsLeft className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Flow List */}
      {!isCollapsed && (
        <div
          className={`overflow-y-auto p-2 flex-1 transition-all duration-300 ease-in-out ${
            isCollapsed
              ? 'opacity-0 max-h-0 pointer-events-none scale-95'
              : 'opacity-100 max-h-full scale-100'
          }`}
        >
          <div className="space-y-2">
            {flows.map(flow => (
              <div key={flow.id} className="z-10">
                <div
                  className={cn(
                    'flex items-center justify-between rounded-md transition-colors cursor-pointer',
                    activeFlowId === flow.id ? 'bg-tertiary/10' : 'hover:bg-secondary-50',
                    editingFlowId === flow.id ? 'p-0' : 'p-3'
                  )}
                  onClick={() => handleFlowClick(flow.id)}
                  onDoubleClick={() => {
                    if (flow.type !== 'Default') handleEditFlow(flow.id, flow.name);
                  }}
                >
                  <div className="flex-1 relative">
                    {editingFlowId === flow.id ? (
                      <>
                        <input
                          type="text"
                          value={editingName}
                          onChange={e => setEditingName(e.target.value)}
                          className="font-medium text-secondary-900 text-sm border border-primary-600 rounded px-4 py-3 bg-none w-full focus:outline-none"
                          onKeyDown={e => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') setEditingFlowId(null);
                          }}
                          onBlur={handleSaveEdit}
                          autoFocus
                        />
                        {editingName && (
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
                            tabIndex={-1}
                            onMouseDown={e => {
                              e.preventDefault();
                              setEditingName('');
                            }}
                          >
                            <X className="pr-1 w-7 h-7" />
                          </button>
                        )}
                      </>
                    ) : (
                      <div className="flex justify-start gap-2 items-center">
                        <div
                          className={cn(
                            'text-xs text-tertiary-600 truncate max-w-28',
                            activeFlowId === flow.id ? 'font-medium' : 'opacity-50'
                          )}
                          title={flow.name}
                        >
                          {flow.name}
                        </div>
                        <div className="text-xs bg-primary-400/10 px-2 py-1 tracking-wide rounded-md text-primary-400">
                          {flow.type}
                        </div>
                      </div>
                    )}
                  </div>
                  {/* dropdown for flows */}
                  {editingFlowId !== flow.id && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button
                          aria-label="more-options"
                          data-testid="more-options-btn"
                          className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
                        >
                          <MoreVertical className="w-4 h-4 text-tertiary-600" />
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="-top-20 w-48 z-50">
                        <DropdownMenuItem
                          onClick={() => handleDuplicateFlow(flow.id)}
                          className="flex items-center gap-2"
                        >
                          <Copy className="w-4 h-4" />
                          <span>{t('common.duplicate')}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center gap-2">
                          <FileClock className="w-4 h-4" />
                          <span>{t('common.versionHistory')}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => flow.type !== 'Default' && handleDeleteFlow(flow.id)}
                          data-testid="delete-flow-btn"
                          className={cn(
                            `flex items-center gap-2 ${
                              flow.type === 'Default'
                                ? 'text-error-200 cursor-not-allowed'
                                : 'text-error-400 hover:bg-error-50'
                            }`
                          )}
                          disabled={flow.type === 'Default'}
                        >
                          <Trash className="w-4 h-4" />
                          <span>{t('common.delete')}</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
