import React from 'react';
import { cn } from '@/lib/utils';
import { getModuleIcon, getModuleText } from '@/modules/editor/utils/config';
import { StencilNodesType } from '../../utils/constants';

interface NodeItemProps {
  type: StencilNodesType; // Use StencilNodesType for stricter typing
  startDrag: (event: React.PointerEvent, type: string) => void;
  isComingSoon?: boolean;
}

const NodeItem: React.FC<NodeItemProps> = ({ type, startDrag, isComingSoon }) => {
  return (
    <div
      className={cn(
        'rounded-lg hover:border-primary-300 hover:shadow-sm transition-all cursor-pointer bg-white flex flex-col gap-3 w-16',
        { 'opacity-50 select-none': isComingSoon }
      )}
      onPointerDown={event => startDrag(event, type)}
    >
      <div className="rounded-lg flex items-center justify-center flex-shrink-0">
        <img src={getModuleIcon(type) || '/placeholder.svg'} className="" alt={type} />
      </div>
      <p className="text-xs text-center text-wrap text-primary-600 w-full">{getModuleText(type)}</p>
    </div>
  );
};

export default NodeItem;
