import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import FlowsPanel from '../widget/flowsPanel';
import { describe, expect, it } from 'vitest';
import userEvent from '@testing-library/user-event';

describe('FlowsPanel', () => {
  it('renders default flows', () => {
    render(<FlowsPanel />);
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Fallback')).toBeInTheDocument();
    expect(screen.getByText('Flows')).toBeInTheDocument();
  });

  it('sets active flow on click', () => {
    render(<FlowsPanel />);
    const fallback = screen.getByText('Fallback');
    fireEvent.click(fallback);
    const clickableDiv = fallback.parentElement?.parentElement?.parentElement;
    expect(clickableDiv?.className).toContain('bg-tertiary');
  });

  it('creates a new flow when plus button is clicked', () => {
    render(<FlowsPanel />);
    const plusBtn = screen.getByRole('button', { name: /add-flow/i });
    fireEvent.click(plusBtn);
    expect(screen.getByText(/untitledflow/i)).toBeInTheDocument();
  });

  it('duplicates a flow', async () => {
    render(<FlowsPanel />);
    // Open dropdown for "Welcome"
    const moreBtn = screen.getAllByTestId('more-options-btn')[0];
    await userEvent.click(moreBtn);
    const duplicateBtn = await screen.findByText('Duplicate');
    await userEvent.click(duplicateBtn);
    
    // Wait for the cloned flow to appear
    await waitFor(() => {
      expect(screen.getByText(/Welcome Cloned/)).toBeInTheDocument();
    });
  });

  it('deletes a custom flow', async () => {
    render(<FlowsPanel />);
    
    // Add a custom flow
    const plusBtn = screen.getByTestId('add-flow-btn');
    await userEvent.click(plusBtn);
    
    // Wait for the new flow to appear
    await waitFor(() => {
      expect(screen.getByText(/untitledflow/i)).toBeInTheDocument();
    });
    
    // Find and click the more options button for the new flow
    const moreBtns = screen.getAllByTestId('more-options-btn');
    const customMoreBtn = moreBtns[0]; // New flow is typically first in the list
    await userEvent.click(customMoreBtn);
    
    // Find and click the delete button
    const deleteBtn = await screen.findByTestId('delete-flow-btn');
    await userEvent.click(deleteBtn);
    
    // Wait for the flow to be removed
    await waitFor(() => {
      expect(screen.queryByText(/untitledflow/i)).not.toBeInTheDocument();
    });
  });

  it('does not allow deleting default flows', async () => {
    render(<FlowsPanel />);
    
    // Find the "Welcome" flow's more options button
    const welcomeFlow = await screen.findByText(/Welcome/i);
    const moreBtn = screen.getAllByTestId('more-options-btn')[0];
    await userEvent.click(moreBtn);
    const deleteBtn = await screen.findByTestId('delete-flow-btn');
    
    // Check if it has the disabled class or attribute
    expect(deleteBtn.classList.contains('text-error-200')).toBe(true);
    expect(deleteBtn.classList.contains('cursor-not-allowed')).toBe(true);
  });

  it('enables editing a custom flow name on double click', () => {
    render(<FlowsPanel />);
    const plusBtn = screen.getByTestId('add-flow-btn');
    fireEvent.click(plusBtn);
    const customFlow = screen.getByText(/untitledflow/i);
    fireEvent.doubleClick(customFlow);
    const input = screen.getByDisplayValue(/untitledflow/i);
    expect(input).toBeInTheDocument();
    fireEvent.change(input, { target: { value: 'My Flow' } });
    fireEvent.blur(input);
    expect(screen.getByText('My Flow')).toBeInTheDocument();
  });

  it('collapses and expands the panel', () => {
    render(<FlowsPanel />);
    // Collapse
    const collapseBtn = screen.getByRole('button', { name: /collapse-panel/i });
    fireEvent.click(collapseBtn);
    expect(screen.getByText('Flows')).toBeInTheDocument();
    // Expand
    const expandBtn = screen.getByRole('button', { name: /expand-panel/i });
    fireEvent.click(expandBtn);
    expect(screen.getByText('Flows')).toBeInTheDocument();
  });

  it('shows and hides search input', () => {
    render(<FlowsPanel />);
    const searchBtn = screen.getByTestId('search-btn');
    fireEvent.click(searchBtn);
    expect(screen.getByPlaceholderText('Search flows...')).toBeInTheDocument();
    const closeBtn = screen.getByRole('button', { name: '' });
    fireEvent.click(closeBtn);
    expect(screen.queryByPlaceholderText('Search flows...')).not.toBeInTheDocument();
  });
});
