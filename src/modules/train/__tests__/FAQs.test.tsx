import { render, screen, fireEvent, waitFor } from '@/test/utils';
import * as storeApi from '@/store/api';
import FAQsTab from '../FAQs';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import userEvent from '@testing-library/user-event';

vi.mock('@/store/api', async () => {
  const actual = await vi.importActual('@/store/api');
  return {
    ...actual,
    useGetFaqCategoriesQuery: vi.fn(),
    useCreateFaqCategoryMutation: vi.fn(),
    useCreateFaqTranslationMutation: vi.fn(),
    useUpdateFaqTranslationMutation: vi.fn(),
    useDeleteFaqTranslationMutation: vi.fn(),
    useGetFaqsByCategoryAndLanguageQuery: vi.fn(),
    useGetBotLanguagesQuery: vi.fn(),
    useGetLanguagesQuery: vi.fn(),
    useGetTranslationByFaqIdAndLangIdQuery: vi.fn(),
  };
});

let mockCreateFaqTranslationUnwrap: ReturnType<typeof vi.fn>;
let mockCreateFaqTranslationTrigger: ReturnType<typeof vi.fn>;
let mockUpdateFaqTranslationUnwrap: ReturnType<typeof vi.fn>;
let mockDeleteFaqTranslationUnwrap: ReturnType<typeof vi.fn>;

describe('FAQsTab', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(storeApi.useGetFaqCategoriesQuery).mockReturnValue({
      data: {
        data: {
          items: [
            { id: 'cat1', name: 'Category 1', botId: 'bot1', type: 'FAQ' },
            { id: 'cat2', name: 'Category 2', botId: 'bot1', type: 'FAQ' },
          ],
          totalCount: 2,
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    } as any);

    const createFaqCategoryMock = vi.fn().mockResolvedValue({
      data: {
        id: 'newCat',
        name: 'New Category',
        botId: 'bot1',
        type: 'FAQ',
      },
    });
    vi.mocked(storeApi.useCreateFaqCategoryMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: createFaqCategoryMock,
      })),
      { isLoading: false, isError: false },
    ] as any);

    vi.mocked(storeApi.useGetFaqsByCategoryAndLanguageQuery).mockReturnValue({
      data: {
        data: {
          items: [
            {
              id: 'faq1',
              questions: [
                'What are the product u want?',
                'Another question',
                'Yet another question',
              ],
              answer: 'A1',
              categoryId: 'cat1',
              langId: 'en',
            },
            {
              id: 'faq2',
              questions: ['Can i get delivery on weekends?'],
              answer: 'A2',
              categoryId: 'cat1',
            },
          ],
          totalCount: 2,
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    } as any);

    mockCreateFaqTranslationUnwrap = vi
      .fn()
      .mockRejectedValue(new Error('Failed to create FAQ item'));
    mockCreateFaqTranslationTrigger = vi.fn(() => ({ unwrap: mockCreateFaqTranslationUnwrap }));
    vi.mocked(storeApi.useCreateFaqTranslationMutation).mockReturnValue([
      mockCreateFaqTranslationTrigger,
      { isLoading: false, isError: true },
    ] as any);

    const mockUpdateFaqTranslationUnwrap = vi.fn().mockResolvedValue({});
    vi.mocked(storeApi.useUpdateFaqTranslationMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: mockUpdateFaqTranslationUnwrap,
      })),
      { isLoading: false, isError: false },
    ] as any);

    mockDeleteFaqTranslationUnwrap = vi.fn().mockResolvedValue({});
    vi.mocked(storeApi.useDeleteFaqTranslationMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: mockDeleteFaqTranslationUnwrap,
      })),
      { isLoading: false, isError: false },
    ] as any);

    vi.mocked(storeApi.useGetTranslationByFaqIdAndLangIdQuery).mockReturnValue({
      data: {
        data: null,
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });

    vi.mocked(storeApi.useGetBotLanguagesQuery).mockReturnValue({
      data: {
        data: {
          items: [{ id: 'en', name: 'English', code: 'en', langId: 'en', isDefault: true }],
        },
      },
      isLoading: false,
      isError: false,
    } as any);

    vi.mocked(storeApi.useGetLanguagesQuery).mockReturnValue({
      data: {
        data: {
          items: [{ id: 'en', name: 'English', code: 'en' }],
        },
      },
      isLoading: false,
      isError: false,
    } as any);
  });

  it('renders FAQsTab and displays categories', async () => {
    render(<FAQsTab />);

    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Questions & Answers')).toBeInTheDocument();
    expect(screen.getByText('Category 1')).toBeInTheDocument();
    expect(screen.getByText('Category 2')).toBeInTheDocument();
  });

  it('allows selecting a category and displays its questions', async () => {
    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText(/What are the product u want?/i)).toBeInTheDocument();
      expect(screen.getByText(/Can i get delivery on weekends?/i)).toBeInTheDocument();
    });
  });

  it('allows adding a new category', async () => {
    render(<FAQsTab />);

    const addCategoryButton = screen.getByTestId('add-category-button');
    await userEvent.click(addCategoryButton);

    await screen.findByRole('heading', { name: 'ADD CATEGORY' });

    const categoryNameInput = screen.getByLabelText('Category name');
    await userEvent.type(categoryNameInput, 'New Test Category');

    const addButton = screen.getByLabelText('Form Submit Button');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.queryByRole('heading', { name: 'ADD CATEGORY' })).not.toBeInTheDocument();
    });
  });

  it('allows adding a new question to a selected category', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();
    });

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    fireEvent.click(addQuestionButton);

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: 'ADD QUESTION' })).toBeInTheDocument();
    });

    const questionInput = screen.getByPlaceholderText('Enter question');
    await userEvent.type(questionInput, 'New Test Question');

    const answerInput = screen.getByPlaceholderText('Enter answer');
    await userEvent.type(answerInput, 'New Test Answer');

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(screen.queryByRole('heading', { name: 'ADD QUESTION' })).not.toBeInTheDocument();
    });
  });

  it('handles loading state for categories', () => {
    vi.mocked(storeApi.useGetFaqCategoriesQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    expect(screen.getByText('Loading FAQs...')).toBeInTheDocument();
  });

  it('handles error state for categories', () => {
    vi.mocked(storeApi.useGetFaqCategoriesQuery).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    expect(screen.getByText('Error loading FAQs.')).toBeInTheDocument();
  });

  it('handles loading state for FAQ items', async () => {
    vi.mocked(storeApi.useGetFaqsByCategoryAndLanguageQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText('Loading FAQ items...')).toBeInTheDocument();
    });
  });

  it('handles error state for FAQ items', async () => {
    vi.mocked(storeApi.useGetFaqsByCategoryAndLanguageQuery).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: true,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText('Error loading FAQ items.')).toBeInTheDocument();
  });

  it('expands and collapses question card', async () => {
    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    const chevronIcon = screen.getAllByLabelText('Toggle expand question card')[0];
    expect(chevronIcon).not.toHaveClass('rotate-180');

    fireEvent.click(chevronIcon);

    await waitFor(() => {
      expect(chevronIcon).toHaveClass('rotate-180');
    });

    fireEvent.click(chevronIcon);

    await waitFor(() => {
      expect(chevronIcon).not.toHaveClass('rotate-180');
    });
  });

  it('handles category creation failure', async () => {
    const mockCreateFaqCategory = vi.fn().mockRejectedValue(new Error('Failed to create category'));
    vi.mocked(storeApi.useCreateFaqCategoryMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: mockCreateFaqCategory,
      })),
      { isLoading: false, isError: true },
    ] as any);

    render(<FAQsTab />);

    const addCategoryButton = screen.getByTestId('add-category-button');
    await userEvent.click(addCategoryButton);

    const categoryNameInput = screen.getByLabelText('Category name');
    await userEvent.type(categoryNameInput, 'Test Category');

    const addButton = screen.getByLabelText('Form Submit Button');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(mockCreateFaqCategory).toHaveBeenCalled();
    });
  });

  it('handles question creation failure', async () => {
    const mockUnwrapCreateFaqTranslation = vi
      .fn()
      .mockRejectedValue(new Error('Failed to create FAQ item'));
    const mockCreateFaqTranslationTrigger = vi.fn(() => ({
      unwrap: mockUnwrapCreateFaqTranslation,
    }));
    vi.mocked(storeApi.useCreateFaqTranslationMutation).mockReturnValue([
      mockCreateFaqTranslationTrigger,
      { isLoading: false, isError: true },
    ] as any);

    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    await userEvent.click(addQuestionButton);

    const questionInput = screen.getByPlaceholderText('Enter question');
    await userEvent.type(questionInput, 'Failing Question');

    const answerInput = screen.getByPlaceholderText('Enter answer');
    await userEvent.type(answerInput, 'Failing Answer');

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    expect(mockCreateFaqTranslationTrigger).toHaveBeenCalled();
  });

  it('adds multiple questions in the form', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();
    });

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    fireEvent.click(addQuestionButton);

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: 'ADD QUESTION' })).toBeInTheDocument();
    });

    const addAnotherQuestionButton = screen.getByRole('button', { name: 'ADD' });
    fireEvent.click(addAnotherQuestionButton);

    await waitFor(() => {
      const questionInputs = screen.getAllByPlaceholderText('Enter question');
      expect(questionInputs.length).toBeGreaterThan(1);
    });
  });

  it('validates question and answer fields in AddQuestionForm', async () => {
    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    await userEvent.click(addQuestionButton);

    const addButton = screen.getByLabelText('Form Submit Button');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: 'ADD QUESTION' })).toBeInTheDocument();
    });

    const questionInput = screen.getByPlaceholderText('Enter question');
    await userEvent.type(questionInput, 'Valid Question');

    const answerInput = screen.getByPlaceholderText('Enter answer');
    await userEvent.type(answerInput, 'Valid Answer');

    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.queryByRole('heading', { name: 'ADD QUESTION' })).not.toBeInTheDocument();
    });
  });

  it('handles language selection in RightPanelContent', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByTestId('country-flag')).toBeInTheDocument();
    });
  });

  it('handles download and share icon clicks', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    const downloadIcon = screen.getByTestId('download-icon');
    const shareIcon = screen.getByTestId('share-icon');
    await userEvent.click(downloadIcon);
    await userEvent.click(shareIcon);
    //TODO will have to add assertion when this buttons will become functional
  });

  it('handles empty categories list', () => {
    vi.mocked(storeApi.useGetFaqCategoriesQuery).mockReturnValue({
      data: {
        data: {
          items: [],
          totalCount: 0,
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Questions & Answers')).toBeInTheDocument();
  });

  it('handles category selection properly', async () => {
    render(<FAQsTab />);

    const category1 = screen.getByText('Category 1');
    expect(category1.closest('li')).toHaveClass('bg-primary-100');

    await userEvent.click(screen.getByText('Category 2'));

    await waitFor(() => {
      expect(screen.getByText('Category 2').closest('li')).toHaveClass('bg-primary-100');
    });
  });

  it('displays correct question numbering and formatting', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText(/Q What are the product u want?/)).toBeInTheDocument();
      expect(screen.getByText(/A: A1/)).toBeInTheDocument();
      expect(screen.getByText(/Q Can i get delivery on weekends?/)).toBeInTheDocument();
      expect(screen.getByText(/A: A2/)).toBeInTheDocument();
    });
  });

  it('handles form reset on modal close', async () => {
    render(<FAQsTab />);

    const addCategoryButton = screen.getByTestId('add-category-button');
    await userEvent.click(addCategoryButton);

    const categoryNameInput = screen.getByLabelText('Category name');
    await userEvent.type(categoryNameInput, 'Test Category');

    const cancelButton = screen.getByRole('button', { name: 'CANCEL' });
    await userEvent.click(cancelButton);

    await userEvent.click(addCategoryButton);

    await waitFor(() => {
      const categoryNameInput = screen.getByLabelText('Category name');
      expect(categoryNameInput).toHaveValue('');
    });
  });

  it('handles question form with translate functionality', async () => {
    render(<FAQsTab />);

    fireEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();
    });

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    fireEvent.click(addQuestionButton);

    await waitFor(() => {
      expect(screen.getByText('TRANSLATE')).toBeInTheDocument();
      expect(screen.getByText('Translate to:')).toBeInTheDocument();
    });
  });

  it('handles question form with flow linking', async () => {
    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    const addQuestionButton = screen.getByRole('button', { name: 'ADD QUESTION' });
    await userEvent.click(addQuestionButton);

    await waitFor(() => {
      expect(screen.getByText('Link Flow:')).toBeInTheDocument();
      expect(screen.getByText('Choose Flow')).toBeInTheDocument();
    });
  });

  it('displays empty state for FAQ items when no items are present', async () => {
    vi.mocked(storeApi.useGetFaqsByCategoryAndLanguageQuery).mockReturnValue({
      data: {
        data: {
          items: [],
          totalCount: 0,
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    } as any);

    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    await waitFor(() => {
      expect(screen.getByText('Start adding questions')).toBeInTheDocument();
      expect(screen.getByText('Nothing to show')).toBeInTheDocument();
      expect(screen.getAllByRole('button', { name: 'ADD QUESTION' })).toHaveLength(2);
    });
  });

  it('allows editing an existing question', async () => {
    const { baseElement } = render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    const editButton = screen.getAllByLabelText('More options')[0];
    await userEvent.click(editButton);
    await userEvent.click(screen.getByText('Edit'));

    expect(screen.getByRole('heading', { name: 'EDIT QUESTION' })).toBeInTheDocument();

    const questionInput = screen.getByDisplayValue('What are the product u want?');
    await userEvent.clear(questionInput);
    await userEvent.type(questionInput, 'Updated Test Question');

    const answerInput = screen.getByDisplayValue('A1');
    await userEvent.clear(answerInput);
    await userEvent.type(answerInput, 'Updated Test Answer');

    const updateButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(updateButton);

    expect(screen.queryByRole('heading', { name: 'EDIT QUESTION' })).not.toBeInTheDocument();
  });

  it('allows deleting an existing question', async () => {
    render(<FAQsTab />);

    await userEvent.click(screen.getByText('Category 1'));

    const deleteButton = screen.getAllByLabelText('More options')[0];
    await userEvent.click(deleteButton);
    await userEvent.click(screen.getByText('Delete'));
    expect(screen.getByText(/What are the product u want?/)).toBeInTheDocument();

    await waitFor(() => {
      expect(mockDeleteFaqTranslationUnwrap).toHaveBeenCalled();
    });
  });
});
