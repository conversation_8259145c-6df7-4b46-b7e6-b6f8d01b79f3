import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import QuestionCard from './QuestionCard';
import { FaqCategory, FaqItem } from '@/types';
import { useGetFaqsByCategoryAndLanguageQuery, useDeleteFaqTranslationMutation } from '@/store/api';
import AddModal from '../components/AddModal';
import AddQuestionForm from './AddQuestionForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { debounce } from 'lodash';

interface FaqListProps {
  selectedCategory: FaqCategory;
  language: string;
  AddQuestionModal: React.ReactNode;
}

const FaqList: React.FC<FaqListProps> = ({ selectedCategory, language, AddQuestionModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [faqToEdit, setFaqToEdit] = useState<FaqItem | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [allFaqItems, setAllFaqItems] = useState<FaqItem[]>([]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { data, error, isLoading, isFetching, refetch } = useGetFaqsByCategoryAndLanguageQuery(
    {
      categoryId: selectedCategory.id,
      langId: language,
      page,
      limit: 10,
    },
    {
      skip: !language || !hasMore,
      refetchOnMountOrArgChange: true,
    }
  );

  const [deleteFaqTranslation] = useDeleteFaqTranslationMutation();

  useEffect(() => {
    if (data?.data?.items) {
      setAllFaqItems(prevItems => {
        const newItems = data.data.items.filter(
          newItem => !prevItems.some(prevItem => prevItem.id === newItem.id)
        );
        return [...prevItems, ...newItems];
      });
      setHasMore(data.data.pagination.hasNext);
    }
  }, [data]);

  useEffect(() => {
    setAllFaqItems([]);
    setPage(1);
    setHasMore(true);
  }, [selectedCategory, language]);

  const handleScroll = useCallback(
    debounce(() => {
      const { scrollTop, clientHeight, scrollHeight } = scrollContainerRef.current!;
      if (scrollHeight - scrollTop <= clientHeight + 50 && hasMore && !isFetching) {
        setPage(prevPage => prevPage + 1);
      }
    }, 200),
    [hasMore, isFetching]
  );

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  const handleEdit = (faqItem: FaqItem) => {
    setFaqToEdit(faqItem);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (faqItem: FaqItem) => {
    try {
      await deleteFaqTranslation({ id: faqItem.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('faqs.items.questionsDeleted')} />,
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete FAQ item:', error);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setFaqToEdit(null);
  };

  if (isLoading && page === 1) {
    return <div className="text-tertiary-700">{t('faqs.items.loading')}</div>;
  }

  if (error) {
    return <div className="text-error-500">{t('faqs.items.loadingError')}</div>;
  }

  if (!allFaqItems.length && !isLoading)
    return (
      <EmptyState title={t('faqs.items.startAdding')} description={t('common.nothingToShow')}>
        <div className="mt-5">{AddQuestionModal}</div>
      </EmptyState>
    );

  return (
    <div className="space-y-4 overflow-auto h-full" ref={scrollContainerRef}>
      {allFaqItems.map(item => (
        <QuestionCard key={item.id} faqItem={item} onEdit={handleEdit} onDelete={handleDelete} />
      ))}

      {isFetching && <div className="text-center py-2">{t('faqs.items.loading')}</div>}

      {isEditModalOpen && (
        <AddModal
          title={t('faqs.items.editTitle')}
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          className="sm:max-w-[850px]"
        >
          <AddQuestionForm
            categoryId={selectedCategory.id}
            botId={selectedCategory.botId}
            onClose={handleCloseEditModal}
            faqItemNode={faqToEdit}
            selectedLangId={language}
          />
        </AddModal>
      )}
    </div>
  );
};

export default FaqList;
