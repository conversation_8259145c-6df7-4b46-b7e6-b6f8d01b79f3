import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FloatingField } from '@/components/ui/floating-label';
import { Languages, Plus, Sparkles, Trash2 } from 'lucide-react';
import RenderButtons from '@/modules/train/components/RenderButtons';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { FaqItem } from '@/types';
import LanguageDropdown from '@/components/LanguageDropdown';
import { useAddQuestionForm } from '@/modules/train/FAQs/useAddQuestionForm';
import { useTranslation } from 'react-i18next';
import { useFieldArray } from 'react-hook-form';

interface AddQuestionFormProps {
  onClose: () => void;
  categoryId: string;
  botId: string;
  faqItemNode?: FaqItem | null;
  selectedLangId: string;
}

function AddQuestionForm({
  onClose,
  categoryId,
  botId,
  faqItemNode,
  selectedLangId,
}: AddQuestionFormProps) {
  const { form, onSubmit, isLoadingTranslation, initialData } = useAddQuestionForm({
    onClose,
    categoryId,
    botId,
    faqItemNode,
    selectedLangId,
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'questions',
  });

  const { t } = useTranslation();

  //TODO: need to use shimmer
  if (isLoadingTranslation) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form className="h-0 flex flex-col flex-1" onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="flex flex-col h-0 flex-1">
          <CardContent className="pt-5 flex flex-col h-0 flex-1">
            <FormField
              control={form.control}
              name="langId"
              render={({ field }) => (
                <FormItem className="mb-4 flex flex-col">
                  <FormControl>
                    <LanguageDropdown onChange={field.onChange} initialValue={field.value} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Questions Section */}
            <FormLabel className="pb-2">{t('faqs.items.questionLabel')}</FormLabel>
            <div className="flex flex-col gap-2 overflow-auto">
              {fields.map((field, index) => (
                <FormField
                  key={field.id}
                  control={form.control}
                  name={`questions.${index}.value`}
                  render={({ field: formField }) => (
                    <FormItem className="flex items-center gap-2 relative">
                      <div className="flex flex-col gap-1 flex-1">
                        <FormControl>
                          <Input placeholder={t('faqs.items.questionPlaceholder')} {...formField} />
                        </FormControl>
                        <FormMessage />
                      </div>
                      {index === 0 && (
                        <div className="bg-tertiary-100 rounded-sm text-xs text-tertiary-500 !m-0 p-1 absolute right-3 top-1/2 -translate-y-1/2">
                          {t('faqs.items.primaryLabel')}
                        </div>
                      )}
                      {index > 0 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute !bg-background right-1 top-1/2 -translate-y-1/2 !m-0 h-4 w-8"
                          onClick={() => remove(index)}
                          data-testid="remove-question-button"
                        >
                          <Trash2 className="h-4 w-4 text-tertiary-500" />
                        </Button>
                      )}
                    </FormItem>
                  )}
                />
              ))}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Button
                type="button"
                variant="ghost"
                className="uppercase pl-0"
                onClick={() => append({ value: '' })}
              >
                <Plus className="!h-5 !w-5" /> {t('common.add')}
              </Button>
              <Button
                type="button"
                variant="ghost"
                className="text-sparkle !bg-sparkle !bg-opacity-5 p-1 font-normal"
                data-testid="generate-button"
              >
                <Sparkles className="h-4 w-4" /> {t('common.generate')}
              </Button>
            </div>

            {/* Answer Section */}
            <FormField
              control={form.control}
              name="answer"
              render={({ field }) => (
                <FormItem className="mt-4">
                  <FormLabel>{t('faqs.items.answerLabel')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('faqs.items.answerPlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="border-t py-3">
            {/* Translate and Link Intent Section */}
            <FormField
              control={form.control}
              name="translateTo"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between flex-1">
                  <div className="flex gap-3 items-center">
                    <div className="flex gap-1 items-center text-tertiary-600 opacity-85">
                      <Languages className="w-5 h-5" />
                      <FormLabel>{t('common.translateTo')}:</FormLabel>
                    </div>

                    <LanguageDropdown onChange={field.onChange} initialValue={field.value} />
                  </div>

                  <Button type="button" variant="ghost" className="opacity-70 font-normal">
                    {t('common.translate')}
                  </Button>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardFooter>
        </Card>

        <FormField
          control={form.control}
          name="flowId"
          render={({ field }) => (
            <FormItem className="mt-4 flex gap-2 items-center">
              <FormLabel className="text-nowrap">{t('faqs.items.linkFlowLabel')}:</FormLabel>
              {/* //TODO: implement this  */}
              <FloatingField
                as="select"
                id="flowId"
                label={t('faqs.items.chooseFlowPlaceholder')}
                {...field}
                options={[{ value: 'newintent', label: 'Newintent' }]}
                className="data-[placeholder]:text-tertiary-500 w-56"
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!initialData}
        />
      </form>
    </Form>
  );
}

export default AddQuestionForm;
